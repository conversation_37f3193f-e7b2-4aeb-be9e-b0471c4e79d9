# Hướng dẫn test ứng dụng Google Sheets với nhiều sheet

## Cách tạo Google Sheet test:

1. **Tạo Google Sheet mới** tại https://sheets.google.com
2. **Tạo Sheet 1** với dữ liệu:
   ```
   Tên sản phẩm | Gi<PERSON> | Số lượng | Danh mục
   iPhone 15    | 25000000 | 10 | Điện thoại
   Samsung S24  | 22000000 | 15 | Điện thoại  
   MacBook Pro  | 45000000 | 5  | Laptop
   Dell XPS     | 35000000 | 8  | Laptop
   ```

3. **Tạo Sheet 2** (click dấu + ở dưới) với dữ liệu:
   ```
   Tháng | Doanh thu | Chi phí | Lợi nhuận
   1     | 100000000 | 70000000 | 30000000
   2     | 120000000 | 80000000 | 40000000
   3     | 150000000 | 90000000 | 60000000
   4     | 180000000 | 100000000 | 80000000
   ```

4. **Chia sẻ công khai**:
   - Click "Share" (Chia sẻ)
   - <PERSON><PERSON><PERSON> "Anyone with the link" (Bất kỳ ai có đường link)
   - Copy URL

## Tính năng mới:

✅ **Tự động phát hiện nhiều sheet**
✅ **Dropdown chọn sheet** 
✅ **Hiển thị thông tin sheet hiện tại**
✅ **Loading indicator** khi chuyển sheet
✅ **Dữ liệu realtime** khi chuyển đổi

## Cách test:

1. Mở http://localhost:5173/
2. Nhập URL Google Sheet
3. Sau khi kết nối, bạn sẽ thấy dropdown chọn sheet (nếu có > 1 sheet)
4. Chuyển đổi giữa các sheet để xem dữ liệu khác nhau
5. Test cả chế độ bảng và biểu đồ cho mỗi sheet
