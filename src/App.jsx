import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const EnhancedGoogleSheetsApp = () => {
  const [data, setData] = useState([]);
  const [headers, setHeaders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [sheetUrl, setSheetUrl] = useState('');
  const [connected, setConnected] = useState(false);
  const [activeView, setActiveView] = useState('table');
  const [selectedNumericColumn, setSelectedNumericColumn] = useState('');
  const [selectedCategoryColumn, setSelectedCategoryColumn] = useState('');
  const [numericColumns, setNumericColumns] = useState([]);
  const [sheets, setSheets] = useState([]);
  const [selectedSheet, setSelectedSheet] = useState(null);
  const [loadingSheets, setLoadingSheets] = useState(false);
  const [debugInfo, setDebugInfo] = useState('');

  const parseCSV = (csvText) => {
    try {
      // Đơn giản hóa: split theo dòng và dấu phẩy
      const lines = csvText.split('\n').filter(line => line.trim());
      const result = [];

      for (let line of lines) {
        // Xử lý quotes đơn giản
        const row = line.split(',').map(cell => {
          // Loại bỏ quotes ở đầu và cuối
          let cleaned = cell.trim();
          if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
            cleaned = cleaned.slice(1, -1);
          }
          return cleaned;
        });

        result.push(row);
      }

      console.log('CSV parsing result:', result.length, 'rows');
      return result;
    } catch (error) {
      console.error('CSV parsing error:', error);
      throw new Error('Lỗi khi phân tích dữ liệu CSV');
    }
  };

  const detectSheetsByTesting = async (sheetId) => {
    console.log('Testing different GIDs to detect sheets...');
    const detectedSheets = [];

    // Test các GID từ 0 đến 10
    const gidsToTest = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'];

    for (const gid of gidsToTest) {
      try {
        console.log(`Testing GID ${gid}...`);
        setDebugInfo && setDebugInfo(`Đang test sheet ${parseInt(gid) + 1}...`);

        const testUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/export?format=csv&gid=${gid}`;

        // Thử direct fetch trước
        let response;
        try {
          response = await fetch(testUrl);
        } catch (directError) {
          // Nếu direct không được, thử proxy
          const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(testUrl)}`;
          response = await fetch(proxyUrl);
        }

        if (response.ok) {
          const csvData = await response.text();
          console.log(`GID ${gid} response length:`, csvData.length);

          if (csvData && csvData.trim().length > 10) { // Có dữ liệu thực sự
            // Thử lấy tên sheet từ dòng đầu hoặc dùng tên mặc định
            const firstLine = csvData.split('\n')[0];
            const sheetName = firstLine && firstLine.length > 0 ? `Sheet${parseInt(gid) + 1}` : `Sheet${parseInt(gid) + 1}`;

            detectedSheets.push({
              title: sheetName,
              id: gid
            });

            console.log(`✓ Found sheet with GID ${gid}: ${sheetName}`);
          } else {
            console.log(`✗ GID ${gid} has no data`);
          }
        } else {
          console.log(`✗ GID ${gid} failed with status:`, response.status);
        }
      } catch (error) {
        console.log(`✗ GID ${gid} error:`, error.message);
      }

      // Thêm delay nhỏ để tránh rate limit
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('Detection complete. Found sheets:', detectedSheets);
    return detectedSheets;
  };

  const fetchSheetsMetadata = async (sheetId) => {
    try {
      setLoadingSheets(true);
      console.log('Fetching metadata for sheet ID:', sheetId);

      // Phương pháp chính: Test trực tiếp các GID
      const detectedSheets = await detectSheetsByTesting(sheetId);

      if (detectedSheets.length > 0) {
        console.log('Successfully detected sheets:', detectedSheets);
        return detectedSheets;
      }

      // Fallback: Nếu không detect được sheet nào
      console.log('No sheets detected, using fallback');
      return [{ title: 'Sheet1', id: '0' }];
    } catch (err) {
      console.warn('Không thể lấy metadata sheets, sử dụng sheet mặc định:', err);
      return [{ title: 'Sheet1', id: '0' }];
    } finally {
      setLoadingSheets(false);
    }
  };

  const fetchGoogleSheetData = async (url, gid = null) => {
    try {
      console.log('Fetching data for URL:', url, 'GID:', gid);

      const sheetIdMatch = url.match(/[-\w]{25,}/);
      if (!sheetIdMatch) {
        throw new Error('URL Google Sheet không hợp lệ');
      }

      const sheetId = sheetIdMatch[0];
      let csvUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/export?format=csv`;

      if (gid !== null && gid !== '0') {
        csvUrl += `&gid=${gid}`;
      }

      console.log('CSV URL:', csvUrl);
      let csvText = '';

      try {
        // Thử trực tiếp trước với timeout
        console.log('Trying direct fetch...');
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5s timeout

        const directResponse = await fetch(csvUrl, {
          signal: controller.signal,
          mode: 'cors'
        });
        clearTimeout(timeoutId);
        console.log('Direct response status:', directResponse.status);

        if (directResponse.ok) {
          csvText = await directResponse.text();
          console.log('Direct fetch successful, data length:', csvText.length);
        } else {
          throw new Error(`Direct fetch failed with status: ${directResponse.status}`);
        }
      } catch (directError) {
        console.log('Direct fetch failed:', directError.message);
        // Nếu trực tiếp không được, dùng proxy nhanh hơn
        try {
          console.log('Trying faster proxy...');
          const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(csvUrl)}`;
          const proxyResponse = await fetch(proxyUrl, {
            headers: {
              'Accept': 'text/csv,text/plain,*/*'
            }
          });
          console.log('Proxy response status:', proxyResponse.status);

          if (!proxyResponse.ok) {
            throw new Error(`Proxy fetch failed with status: ${proxyResponse.status}`);
          }
          csvText = await proxyResponse.text();
          console.log('Proxy fetch successful, data length:', csvText.length);
        } catch (proxyError) {
          console.error('Proxy fetch failed:', proxyError);
          // Thử proxy backup
          try {
            console.log('Trying backup proxy...');
            const backupProxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(csvUrl)}`;
            const backupResponse = await fetch(backupProxyUrl);
            if (backupResponse.ok) {
              const backupData = await backupResponse.json();
              csvText = backupData.contents;
              console.log('Backup proxy successful, data length:', csvText.length);
            } else {
              throw new Error('All proxies failed');
            }
          } catch (backupError) {
            throw new Error('Không thể kết nối với Google Sheet. Hãy đảm bảo sheet đã được chia sẻ công khai và thử lại.');
          }
        }
      }

      if (!csvText || csvText.trim() === '') {
        throw new Error('Google Sheet trống hoặc không có dữ liệu');
      }

      console.log('CSV text preview:', csvText.substring(0, 500));

      const rows = parseCSV(csvText);
      console.log('Parsed rows:', rows.length, 'First row:', rows[0]);

      if (rows.length === 0) {
        throw new Error('Không tìm thấy dữ liệu trong sheet');
      }

      const extractedHeaders = rows[0].map(header => header.replace(/"/g, '').trim());
      console.log('Headers:', extractedHeaders);
      setHeaders(extractedHeaders);

      const extractedData = rows.slice(1)
        .filter(row => row.some(cell => cell && cell.trim()))
        .map(row => {
          const rowData = {};
          row.forEach((cell, index) => {
            const cleanValue = cell.replace(/"/g, '').trim();
            const header = extractedHeaders[index];
            if (header) {
              // Thử convert sang số
              const numValue = parseFloat(cleanValue);
              rowData[header] = !isNaN(numValue) && cleanValue !== ''
                ? numValue
                : cleanValue;
            }
          });
          return rowData;
        });

      console.log('Extracted data:', extractedData.length, 'rows');
      console.log('First data row:', extractedData[0]);
      setData(extractedData);

      const numerics = extractedHeaders.filter(header =>
        extractedData.length > 0 && typeof extractedData[0][header] === 'number'
      );
      console.log('Numeric columns:', numerics);
      setNumericColumns(numerics);

      if (numerics.length > 0) {
        setSelectedNumericColumn(numerics[0]);
      }

      const firstNonNumeric = extractedHeaders.find(header =>
        !numerics.includes(header)
      );

      if (firstNonNumeric) {
        setSelectedCategoryColumn(firstNonNumeric);
      }

      console.log('Data processing completed successfully');

    } catch (err) {
      setError(err.message);
      console.error('Lỗi khi tải Google Sheet:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    if (!sheetUrl) {
      setError('Vui lòng nhập URL Google Sheet');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setDebugInfo('Bắt đầu kết nối...');
      console.log('Bắt đầu kết nối với:', sheetUrl);

      const sheetIdMatch = sheetUrl.match(/[-\w]{25,}/);
      if (!sheetIdMatch) {
        throw new Error('URL Google Sheet không hợp lệ');
      }

      const sheetId = sheetIdMatch[0];
      console.log('Sheet ID:', sheetId);
      setDebugInfo(`Sheet ID: ${sheetId}`);

      // Load dữ liệu và metadata song song để tăng tốc
      setDebugInfo('Đang tải dữ liệu...');

      const [dataResult, metaResult] = await Promise.allSettled([
        fetchGoogleSheetData(sheetUrl, null),
        fetchSheetsMetadata(sheetId)
      ]);

      if (dataResult.status === 'rejected') {
        throw dataResult.reason;
      }

      // Xử lý metadata (không quan trọng nếu fail)
      if (metaResult.status === 'fulfilled') {
        const detectedSheets = metaResult.value;
        console.log('Final detected sheets:', detectedSheets);
        setSheets(detectedSheets);
        setSelectedSheet(detectedSheets[0]);
        setDebugInfo(`Phát hiện ${detectedSheets.length} sheet(s)`);
      } else {
        console.warn('Không thể lấy metadata sheets:', metaResult.reason);
        setSheets([{ title: 'Sheet1', id: '0' }]);
        setSelectedSheet({ title: 'Sheet1', id: '0' });
        setDebugInfo('Sử dụng sheet mặc định');
      }

      setConnected(true);
      setDebugInfo('Kết nối thành công!');
      console.log('Kết nối thành công!');

    } catch (err) {
      console.error('Lỗi kết nối:', err);
      setError(err.message);
      setDebugInfo(`Lỗi: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSheetChange = async (sheet) => {
    setSelectedSheet(sheet);
    await fetchGoogleSheetData(sheetUrl, sheet.id);
  };

  const getViewClass = (view) => {
    if (view === activeView) {
      return 'opacity-100 transition-opacity duration-300';
    }
    return 'hidden opacity-0 transition-opacity duration-300';
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <header className="p-4 bg-white dark:bg-gray-800 shadow-sm backdrop-blur-lg bg-opacity-80 dark:bg-opacity-80">
        <div className="container mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Google Sheets Web App</h1>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-4">
        {!connected ? (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 max-w-3xl mx-auto mt-8 backdrop-blur-lg bg-opacity-90 dark:bg-opacity-90">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Kết nối với Google Sheet</h2>

            <div className="mb-4">
              <label htmlFor="sheet-url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                URL Google Sheet
              </label>
              <input
                id="sheet-url"
                type="text"
                className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                placeholder="https://docs.google.com/spreadsheets/d/..."
                value={sheetUrl}
                onChange={(e) => setSheetUrl(e.target.value)}
              />
              <div className="mt-2 flex gap-2">
                <button
                  onClick={() => setSheetUrl('https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit')}
                  className="text-xs text-blue-500 hover:text-blue-600 underline"
                >
                  Dùng sheet demo
                </button>
                <button
                  onClick={async () => {
                    setSheetUrl('https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit');
                    setTimeout(() => handleConnect(), 100);
                  }}
                  className="text-xs text-green-500 hover:text-green-600 underline"
                >
                  Test ngay
                </button>
                <button
                  onClick={async () => {
                    if (sheetUrl) {
                      const sheetIdMatch = sheetUrl.match(/[-\w]{25,}/);
                      if (sheetIdMatch) {
                        console.log('Manual sheet detection...');
                        const sheets = await detectSheetsByTesting(sheetIdMatch[0]);
                        console.log('Manual detection result:', sheets);
                        setSheets(sheets);
                        if (sheets.length > 0) {
                          setSelectedSheet(sheets[0]);
                        }
                      }
                    }
                  }}
                  className="text-xs text-purple-500 hover:text-purple-600 underline"
                >
                  Detect sheets
                </button>
              </div>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Đảm bảo Google Sheet của bạn đã được chia sẻ công khai (chế độ "Bất kỳ ai có đường link")
              </p>
            </div>

            <button
              onClick={() => {
                console.log('Button clicked!', sheetUrl);
                handleConnect();
              }}
              disabled={loading || !sheetUrl}
              className={`px-4 py-2 rounded-lg font-medium text-white transition duration-200 ${
                loading || !sheetUrl
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600 focus:ring-2 focus:ring-blue-300'
              }`}
            >
              {loading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Đang kết nối...
                </span>
              ) : 'Kết nối'}
            </button>

            {error && (
              <div className="mt-4 p-3 bg-red-100 border border-red-200 text-red-700 rounded-lg dark:bg-red-900 dark:text-red-100 dark:border-red-800">
                {error}
              </div>
            )}

            {debugInfo && (
              <div className="mt-4 p-3 bg-blue-100 border border-blue-200 text-blue-700 rounded-lg dark:bg-blue-900 dark:text-blue-100 dark:border-blue-800">
                Debug: {debugInfo}
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white">Dữ liệu từ Google Sheet</h2>
                {sheets.length > 0 && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Tìm thấy {sheets.length} sheet{sheets.length > 1 ? 's' : ''} • Hiện tại: {selectedSheet?.title}
                    {sheets.length > 1 && (
                      <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full dark:bg-green-900 dark:text-green-200">
                        Nhiều sheet
                      </span>
                    )}
                  </p>
                )}
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                {sheets.length > 1 && (
                  <div className="flex items-center gap-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Sheet:
                    </label>
                    <select
                      value={selectedSheet?.id || ''}
                      onChange={(e) => {
                        const sheet = sheets.find(s => s.id === e.target.value);
                        if (sheet) handleSheetChange(sheet);
                      }}
                      disabled={loading}
                      className="px-3 py-1.5 text-sm rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                    >
                      {sheets.map(sheet => (
                        <option key={sheet.id} value={sheet.id}>
                          {sheet.title}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <button
                  onClick={() => {
                    setConnected(false);
                    setSheets([]);
                    setSelectedSheet(null);
                    setData([]);
                    setHeaders([]);
                  }}
                  className="px-3 py-1.5 text-sm rounded-lg font-medium text-white bg-blue-500 hover:bg-blue-600 focus:ring-2 focus:ring-blue-300 transition duration-200"
                >
                  Kết nối lại
                </button>
              </div>
            </div>

            {loading && (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-8 text-center backdrop-blur-lg bg-opacity-90 dark:bg-opacity-90">
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="text-gray-700 dark:text-gray-300">
                    {debugInfo || 'Đang tải...'}
                  </span>
                </div>
              </div>
            )}

            {data.length > 0 && !loading && (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden backdrop-blur-lg bg-opacity-90 dark:bg-opacity-90">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setActiveView('table')}
                      className={`px-3 py-1.5 text-sm rounded-lg font-medium transition duration-200 ${
                        activeView === 'table'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      Bảng dữ liệu
                    </button>
                    <button
                      onClick={() => setActiveView('chart')}
                      className={`px-3 py-1.5 text-sm rounded-lg font-medium transition duration-200 ${
                        activeView === 'chart'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      Biểu đồ
                    </button>
                  </div>
                </div>

                <div className={getViewClass('table')}>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          {headers.map((header, index) => (
                            <th
                              key={index}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                            >
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {data.map((row, rowIndex) => (
                          <tr key={rowIndex} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                            {headers.map((header, cellIndex) => (
                              <td
                                key={cellIndex}
                                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
                              >
                                {row[header]}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className={getViewClass('chart')}>
                  {numericColumns.length > 0 ? (
                    <div className="p-4">
                      <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Chọn trường dữ liệu
                          </label>
                          <select
                            value={selectedNumericColumn}
                            onChange={(e) => setSelectedNumericColumn(e.target.value)}
                            className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                          >
                            {numericColumns.map(column => (
                              <option key={column} value={column}>{column}</option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Phân loại theo
                          </label>
                          <select
                            value={selectedCategoryColumn}
                            onChange={(e) => setSelectedCategoryColumn(e.target.value)}
                            className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                          >
                            {headers.map(header => (
                              <option key={header} value={header}>{header}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={data}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#555" />
                            <XAxis
                              dataKey={selectedCategoryColumn}
                              tick={{fill: '#999'}}
                              axisLine={{stroke: '#555'}}
                            />
                            <YAxis
                              tick={{fill: '#999'}}
                              axisLine={{stroke: '#555'}}
                            />
                            <Tooltip
                              contentStyle={{
                                backgroundColor: 'rgba(50, 50, 50, 0.8)',
                                border: 'none',
                                borderRadius: '8px',
                                color: '#fff'
                              }}
                            />
                            <Legend />
                            <Bar
                              dataKey={selectedNumericColumn}
                              fill="#3b82f6"
                              animationDuration={1000}
                              radius={[4, 4, 0, 0]}
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  ) : (
                    <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                      Không tìm thấy dữ liệu số để tạo biểu đồ. Vui lòng đảm bảo bảng tính của bạn có ít nhất một cột chứa dữ liệu số.
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </main>

      <footer className="p-4 bg-white dark:bg-gray-800 shadow-sm mt-auto backdrop-blur-lg bg-opacity-80 dark:bg-opacity-80">
        <div className="container mx-auto text-center text-sm text-gray-500 dark:text-gray-400">
          &copy; {new Date().getFullYear()} Google Sheets Web App
        </div>
      </footer>
    </div>
  );
};

export default EnhancedGoogleSheetsApp;
