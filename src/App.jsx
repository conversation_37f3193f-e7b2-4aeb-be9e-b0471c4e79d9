import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const EnhancedGoogleSheetsApp = () => {
  const [data, setData] = useState([]);
  const [headers, setHeaders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [sheetUrl, setSheetUrl] = useState('https://docs.google.com/spreadsheets/d/1puKECNQhgaRs9-d7nqHH6U9qTvGiWlchEes9CCpHCbI/edit?gid=394890302#gid=394890302');
  const [connected, setConnected] = useState(false);
  const [activeView, setActiveView] = useState('table');
  const [selectedNumericColumn, setSelectedNumericColumn] = useState('');
  const [selectedCategoryColumn, setSelectedCategoryColumn] = useState('');
  const [numericColumns, setNumericColumns] = useState([]);
  const [sheets, setSheets] = useState([]);
  const [selectedSheet, setSelectedSheet] = useState(null);
  const [loadingSheets, setLoadingSheets] = useState(false);
  const [debugInfo, setDebugInfo] = useState('');
  const [lastSyncTime, setLastSyncTime] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [filteredData, setFilteredData] = useState([]);
  const [dashboardMode, setDashboardMode] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Auto connect khi load trang
  useEffect(() => {
    if (sheetUrl && !connected) {
      console.log('Auto connecting...');
      handleConnect();
    }
  }, []);

  // Real-time sync every 60 seconds
  useEffect(() => {
    if (connected && selectedSheet) {
      const interval = setInterval(() => {
        console.log('Auto refreshing data...');
        handleRefresh();
      }, 60000); // 60 seconds

      return () => clearInterval(interval);
    }
  }, [connected, selectedSheet]);

  // Filter data based on search query
  useEffect(() => {
    if (!data.length) {
      setFilteredData([]);
      return;
    }

    let filtered = data;
    
    if (searchQuery.trim()) {
      filtered = data.filter(row =>
        Object.values(row).some(value =>
          value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Apply sorting
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        const aVal = a[sortConfig.key];
        const bVal = b[sortConfig.key];
        
        if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    setFilteredData(filtered);
  }, [data, searchQuery, sortConfig]);

  const parseCSV = (csvText) => {
    try {
      // Đơn giản hóa: split theo dòng và dấu phẩy
      const lines = csvText.split('\n').filter(line => line.trim());
      const result = [];
      
      for (let line of lines) {
        // Xử lý quotes đơn giản
        const row = line.split(',').map(cell => {
          // Loại bỏ quotes ở đầu và cuối
          let cleaned = cell.trim();
          if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
            cleaned = cleaned.slice(1, -1);
          }
          return cleaned;
        });
        
        result.push(row);
      }
      
      console.log('CSV parsing result:', result.length, 'rows');
      return result;
    } catch (error) {
      console.error('CSV parsing error:', error);
      throw new Error('Lỗi khi phân tích dữ liệu CSV');
    }
  };

  const detectSheetsByTesting = async (sheetId, originalUrl = '') => {
    console.log('Testing different GIDs to detect sheets...');
    const detectedSheets = [];
    
    // GID cụ thể cho 2 sheet của bạn
    const knownGids = [
      { gid: '394890302', name: 'Ninh' },
      { gid: '1715384629', name: 'Mr Hùng' }
    ];
    
    // Extract GID từ URL gốc nếu có
    const urlGidMatch = originalUrl.match(/[#&]gid=(\d+)/);
    const urlGid = urlGidMatch ? urlGidMatch[1] : null;
    
    // Test các GID đã biết trước
    const gidsToTest = [];
    
    // Thêm GID từ URL nếu có
    if (urlGid) {
      const knownSheet = knownGids.find(sheet => sheet.gid === urlGid);
      if (knownSheet) {
        gidsToTest.push({ gid: urlGid, name: knownSheet.name });
      } else {
        gidsToTest.push({ gid: urlGid, name: 'Sheet hiện tại' });
      }
      console.log('Found GID in URL:', urlGid);
    }
    
    // Thêm tất cả GID đã biết
    knownGids.forEach(sheet => {
      if (!gidsToTest.find(g => g.gid === sheet.gid)) {
        gidsToTest.push(sheet);
      }
    });
    
    // Thêm các GID thông thường để backup
    ['0', '1', '2', '3', '4', '5'].forEach(gid => {
      if (!gidsToTest.find(g => g.gid === gid)) {
        gidsToTest.push({ gid, name: `Sheet${parseInt(gid) + 1}` });
      }
    });

    for (const sheetInfo of gidsToTest) {
      try {
        const gid = sheetInfo.gid;
        const sheetName = sheetInfo.name;
        
        console.log(`Testing GID ${gid} (${sheetName})...`);
        setDebugInfo && setDebugInfo(`Đang test ${sheetName}...`);
        
        const testUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/export?format=csv&gid=${gid}`;
        
        // Thử direct fetch trước
        let response;
        try {
          response = await fetch(testUrl);
        } catch (directError) {
          // Nếu direct không được, thử proxy
          const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(testUrl)}`;
          response = await fetch(proxyUrl);
        }
        
        if (response.ok) {
          const csvData = await response.text();
          console.log(`GID ${gid} (${sheetName}) response length:`, csvData.length);
          
          if (csvData && csvData.trim().length > 10) { // Có dữ liệu thực sự
            detectedSheets.push({
              title: sheetName,
              id: gid
            });
            
            console.log(`✓ Found sheet with GID ${gid}: ${sheetName}`);
          } else {
            console.log(`✗ GID ${gid} (${sheetName}) has no data`);
          }
        } else {
          console.log(`✗ GID ${gid} (${sheetName}) failed with status:`, response.status);
        }
      } catch (error) {
        console.log(`✗ GID ${sheetInfo.gid} (${sheetInfo.name}) error:`, error.message);
      }
      
      // Thêm delay nhỏ để tránh rate limit
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    console.log('Detection complete. Found sheets:', detectedSheets);
    return detectedSheets;
  };

  const fetchSheetsMetadata = async (sheetId) => {
    try {
      setLoadingSheets(true);
      console.log('Fetching metadata for sheet ID:', sheetId);
      
      // Phương pháp chính: Test trực tiếp các GID
      const detectedSheets = await detectSheetsByTesting(sheetId, '');
      
      if (detectedSheets.length > 0) {
        console.log('Successfully detected sheets:', detectedSheets);
        return detectedSheets;
      }
      
      // Fallback: Nếu không detect được sheet nào
      console.log('No sheets detected, using fallback');
      return [{ title: 'Sheet1', id: '0' }];
    } catch (err) {
      console.warn('Không thể lấy metadata sheets, sử dụng sheet mặc định:', err);
      return [{ title: 'Sheet1', id: '0' }];
    } finally {
      setLoadingSheets(false);
    }
  };

  const fetchGoogleSheetData = async (url, gid = null) => {
    try {
      console.log('Fetching data for URL:', url, 'GID:', gid);
      
      const sheetIdMatch = url.match(/[-\w]{25,}/);
      if (!sheetIdMatch) {
        throw new Error('URL Google Sheet không hợp lệ');
      }
      
      const sheetId = sheetIdMatch[0];
      let csvUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/export?format=csv`;
      
      if (gid !== null && gid !== '0') {
        csvUrl += `&gid=${gid}`;
      }
      
      console.log('CSV URL:', csvUrl);
      let csvText = '';
      
      try {
        // Thử trực tiếp trước với timeout
        console.log('Trying direct fetch...');
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5s timeout
        
        const directResponse = await fetch(csvUrl, { 
          signal: controller.signal,
          mode: 'cors'
        });
        clearTimeout(timeoutId);
        console.log('Direct response status:', directResponse.status);
        
        if (directResponse.ok) {
          csvText = await directResponse.text();
          console.log('Direct fetch successful, data length:', csvText.length);
        } else {
          throw new Error(`Direct fetch failed with status: ${directResponse.status}`);
        }
      } catch (directError) {
        console.log('Direct fetch failed:', directError.message);
        // Nếu trực tiếp không được, dùng proxy nhanh hơn
        try {
          console.log('Trying faster proxy...');
          const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(csvUrl)}`;
          const proxyResponse = await fetch(proxyUrl, {
            headers: {
              'Accept': 'text/csv,text/plain,*/*'
            }
          });
          console.log('Proxy response status:', proxyResponse.status);
          
          if (!proxyResponse.ok) {
            throw new Error(`Proxy fetch failed with status: ${proxyResponse.status}`);
          }
          csvText = await proxyResponse.text();
          console.log('Proxy fetch successful, data length:', csvText.length);
        } catch (proxyError) {
          console.error('Proxy fetch failed:', proxyError);
          // Thử proxy backup
          try {
            console.log('Trying backup proxy...');
            const backupProxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(csvUrl)}`;
            const backupResponse = await fetch(backupProxyUrl);
            if (backupResponse.ok) {
              const backupData = await backupResponse.json();
              csvText = backupData.contents;
              console.log('Backup proxy successful, data length:', csvText.length);
            } else {
              throw new Error('All proxies failed');
            }
          } catch (backupError) {
            throw new Error('Không thể kết nối với Google Sheet. Hãy đảm bảo sheet đã được chia sẻ công khai và thử lại.');
          }
        }
      }
      
      if (!csvText || csvText.trim() === '') {
        throw new Error('Google Sheet trống hoặc không có dữ liệu');
      }
      
      console.log('CSV text preview:', csvText.substring(0, 500));
      
      const rows = parseCSV(csvText);
      console.log('Parsed rows:', rows.length, 'First row:', rows[0]);
      
      if (rows.length === 0) {
        throw new Error('Không tìm thấy dữ liệu trong sheet');
      }
      
      const extractedHeaders = rows[0].map(header => header.replace(/"/g, '').trim());
      console.log('Headers:', extractedHeaders);
      setHeaders(extractedHeaders);
      
      const extractedData = rows.slice(1)
        .filter(row => row.some(cell => cell && cell.trim()))
        .map(row => {
          const rowData = {};
          row.forEach((cell, index) => {
            const cleanValue = cell.replace(/"/g, '').trim();
            const header = extractedHeaders[index];
            if (header) {
              // Thử convert sang số
              const numValue = parseFloat(cleanValue);
              rowData[header] = !isNaN(numValue) && cleanValue !== '' 
                ? numValue 
                : cleanValue;
            }
          });
          return rowData;
        });
      
      console.log('Extracted data:', extractedData.length, 'rows');
      console.log('First data row:', extractedData[0]);
      setData(extractedData);
      
      const numerics = extractedHeaders.filter(header => 
        extractedData.length > 0 && typeof extractedData[0][header] === 'number'
      );
      console.log('Numeric columns:', numerics);
      setNumericColumns(numerics);
      
      if (numerics.length > 0) {
        setSelectedNumericColumn(numerics[0]);
      }
      
      const firstNonNumeric = extractedHeaders.find(header => 
        !numerics.includes(header)
      );
      
      if (firstNonNumeric) {
        setSelectedCategoryColumn(firstNonNumeric);
      }
      
      console.log('Data processing completed successfully');

    } catch (err) {
      setError(err.message);
      console.error('Lỗi khi tải Google Sheet:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    if (!sheetUrl) {
      setError('Vui lòng nhập URL Google Sheet');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setDebugInfo('Bắt đầu kết nối...');
      console.log('Bắt đầu kết nối với:', sheetUrl);

      const sheetIdMatch = sheetUrl.match(/[-\w]{25,}/);
      if (!sheetIdMatch) {
        throw new Error('URL Google Sheet không hợp lệ');
      }

      const sheetId = sheetIdMatch[0];
      console.log('Sheet ID:', sheetId);
      setDebugInfo(`Sheet ID: ${sheetId}`);

      // Load dữ liệu và metadata song song để tăng tốc
      setDebugInfo('Đang tải dữ liệu...');

      const [dataResult, metaResult] = await Promise.allSettled([
        fetchGoogleSheetData(sheetUrl, null),
        detectSheetsByTesting(sheetId, sheetUrl)
      ]);

      if (dataResult.status === 'rejected') {
        throw dataResult.reason;
      }

      // Xử lý kết quả detect sheets
      if (metaResult.status === 'fulfilled') {
        const detectedSheets = metaResult.value;
        console.log('Final detected sheets:', detectedSheets);
        setSheets(detectedSheets);
        setSelectedSheet(detectedSheets[0]);
        setDebugInfo(`Phát hiện ${detectedSheets.length} sheet(s)`);
      } else {
        console.warn('Không thể detect sheets:', metaResult.reason);
        setSheets([{ title: 'Sheet1', id: '0' }]);
        setSelectedSheet({ title: 'Sheet1', id: '0' });
        setDebugInfo('Sử dụng sheet mặc định');
      }

      setConnected(true);
      setLastSyncTime(new Date());
      setDebugInfo('Kết nối thành công!');
      console.log('Kết nối thành công!');

    } catch (err) {
      console.error('Lỗi kết nối:', err);
      setError(err.message);
      setDebugInfo(`Lỗi: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSheetChange = async (sheet) => {
    setSelectedSheet(sheet);
    await fetchGoogleSheetData(sheetUrl, sheet.id);
    setLastSyncTime(new Date());
  };

  const handleRefresh = async () => {
    if (!selectedSheet) return;

    setRefreshing(true);
    try {
      await fetchGoogleSheetData(sheetUrl, selectedSheet.id);
      setLastSyncTime(new Date());
      setDebugInfo('Đã cập nhật dữ liệu');
    } catch (error) {
      setError('Lỗi khi cập nhật: ' + error.message);
    } finally {
      setRefreshing(false);
    }
  };

  const handleSort = (columnKey) => {
    let direction = 'asc';
    if (sortConfig.key === columnKey && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key: columnKey, direction });
  };

  const formatLastSync = () => {
    if (!lastSyncTime) return '';
    const now = new Date();
    const diff = Math.floor((now - lastSyncTime) / 1000);

    if (diff < 60) return `${diff}s trước`;
    if (diff < 3600) return `${Math.floor(diff / 60)}m trước`;
    return `${Math.floor(diff / 3600)}h trước`;
  };

  const getViewClass = (view) => {
    if (view === activeView) {
      return 'opacity-100 transition-opacity duration-300';
    }
    return 'hidden opacity-0 transition-opacity duration-300';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 bg-white/10 backdrop-blur-xl border-b border-white/20 sticky top-0">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
              📊 Google Sheets
            </h1>
            {lastSyncTime && (
              <div className="flex items-center gap-2 text-xs text-white/60">
                <div className={`w-2 h-2 rounded-full ${refreshing ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'}`}></div>
                <span>Sync {formatLastSync()}</span>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="relative z-10 flex-grow pb-20">
        {!connected ? (
          <div className="container mx-auto px-4 py-8">
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 max-w-md mx-auto">
              <h2 className="text-xl font-semibold mb-4 text-white text-center">
                🚀 Google Sheets - Mr Hùng & Ninh
              </h2>

              {loading && (
                <div className="mb-4 p-4 bg-blue-500/20 border border-blue-400/30 text-blue-100 rounded-2xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                    <span>Đang tự động kết nối...</span>
                  </div>
                </div>
              )}

              {error && (
                <div className="mt-4 p-3 bg-red-500/20 border border-red-400/30 text-red-100 rounded-2xl backdrop-blur-sm">
                  {error}
                </div>
              )}

              {debugInfo && (
                <div className="mt-4 p-3 bg-blue-500/20 border border-blue-400/30 text-blue-100 rounded-2xl backdrop-blur-sm">
                  Debug: {debugInfo}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="container mx-auto px-4 py-4 space-y-4">
            {/* Header with Sheet Info */}
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4">
              <div className="flex flex-col gap-3">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-white">📊 Dữ liệu Sheet</h2>
                  <button
                    onClick={handleRefresh}
                    disabled={refreshing}
                    className="p-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200 disabled:opacity-50"
                  >
                    <span className={`text-white text-sm ${refreshing ? 'animate-spin' : ''}`}>
                      🔄
                    </span>
                  </button>
                </div>

                {sheets.length > 0 && (
                  <div className="flex flex-wrap items-center gap-2 text-sm text-white/80">
                    <span>Tìm thấy {sheets.length} sheet • Hiện tại: {selectedSheet?.title}</span>
                    {sheets.length > 1 && (
                      <span className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full border border-green-400/30">
                        Nhiều sheet
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Search and Controls */}
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4">
              <div className="flex flex-col gap-3">
                {/* Search Bar */}
                <div className="relative">
                  <input
                    type="text"
                    placeholder="🔍 Tìm kiếm dữ liệu..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white"
                    >
                      ✕
                    </button>
                  )}
                </div>

                {/* Controls Row */}
                <div className="flex flex-wrap items-center gap-2">
                  {/* Sheet Selector */}
                  {sheets.length > 0 && (
                    <select
                      value={selectedSheet?.id || ''}
                      onChange={(e) => {
                        const sheet = sheets.find(s => s.id === e.target.value);
                        if (sheet) handleSheetChange(sheet);
                      }}
                      disabled={loading}
                      className="px-3 py-2 bg-white/10 border border-white/20 rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400 disabled:opacity-50"
                    >
                      {sheets.map(sheet => (
                        <option key={sheet.id} value={sheet.id} className="bg-slate-800">
                          {sheet.title}
                        </option>
                      ))}
                    </select>
                  )}

                  {/* Dashboard Toggle */}
                  <button
                    onClick={() => setDashboardMode(!dashboardMode)}
                    className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                      dashboardMode
                        ? 'bg-purple-500/30 text-purple-200 border border-purple-400/50'
                        : 'bg-white/10 text-white/80 border border-white/20 hover:bg-white/20'
                    }`}
                  >
                    {dashboardMode ? '📊 Dashboard' : '📋 Bảng'}
                  </button>

                  {/* Results Count */}
                  {searchQuery && (
                    <span className="text-xs text-white/60 px-2 py-1 bg-white/5 rounded-lg">
                      {filteredData.length} kết quả
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Loading State */}
            {loading && (
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-8 text-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="w-8 h-8 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-white/80">
                    {debugInfo || 'Đang tải...'}
                  </span>
                </div>
              </div>
            )}

            {/* Data Display */}
            {filteredData.length > 0 && !loading && (
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl overflow-hidden">
                {/* View Toggle */}
                <div className="p-4 border-b border-white/20">
                  <div className="flex gap-2">
                    <button
                      onClick={() => setActiveView('table')}
                      className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                        activeView === 'table'
                          ? 'bg-purple-500/30 text-purple-200 border border-purple-400/50'
                          : 'bg-white/10 text-white/80 border border-white/20 hover:bg-white/20'
                      }`}
                    >
                      📋 Bảng
                    </button>
                    <button
                      onClick={() => setActiveView('chart')}
                      className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                        activeView === 'chart'
                          ? 'bg-purple-500/30 text-purple-200 border border-purple-400/50'
                          : 'bg-white/10 text-white/80 border border-white/20 hover:bg-white/20'
                      }`}
                    >
                      📊 Biểu đồ
                    </button>
                  </div>
                </div>

                {/* Table View */}
                <div className={getViewClass('table')}>
                  {/* Mobile Card View */}
                  <div className="block md:hidden p-4 space-y-3">
                    {filteredData.map((row, rowIndex) => (
                      <div key={rowIndex} className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 hover:bg-white/10 transition-all duration-200">
                        {headers.slice(0, 4).map((header, cellIndex) => (
                          <div key={cellIndex} className="flex justify-between items-center py-1">
                            <span className="text-xs text-white/60 font-medium">{header}:</span>
                            <span className="text-sm text-white text-right max-w-[60%] truncate">
                              {row[header]}
                            </span>
                          </div>
                        ))}
                        {headers.length > 4 && (
                          <div className="text-xs text-white/40 mt-2">
                            +{headers.length - 4} cột khác
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Desktop Table View */}
                  <div className="hidden md:block overflow-x-auto">
                    <table className="min-w-full">
                      <thead>
                        <tr className="border-b border-white/20">
                          {headers.map((header, index) => (
                            <th
                              key={index}
                              onClick={() => handleSort(header)}
                              className="px-4 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider cursor-pointer hover:text-white transition-colors duration-200 select-none"
                            >
                              <div className="flex items-center gap-1">
                                {header}
                                {sortConfig.key === header && (
                                  <span className="text-purple-400">
                                    {sortConfig.direction === 'asc' ? '↑' : '↓'}
                                  </span>
                                )}
                              </div>
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {filteredData.map((row, rowIndex) => (
                          <tr key={rowIndex} className="border-b border-white/10 hover:bg-white/5 transition-colors duration-150">
                            {headers.map((header, cellIndex) => (
                              <td
                                key={cellIndex}
                                className="px-4 py-3 text-sm text-white/90 max-w-xs truncate"
                                title={row[header]}
                              >
                                {row[header]}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Chart View */}
                <div className={getViewClass('chart')}>
                  {numericColumns.length > 0 ? (
                    <div className="p-4 space-y-4">
                      {/* Chart Controls */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-white/80 mb-2">
                            📊 Trường dữ liệu
                          </label>
                          <select
                            value={selectedNumericColumn}
                            onChange={(e) => setSelectedNumericColumn(e.target.value)}
                            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
                          >
                            {numericColumns.map(column => (
                              <option key={column} value={column} className="bg-slate-800">
                                {column}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-white/80 mb-2">
                            🏷️ Phân loại theo
                          </label>
                          <select
                            value={selectedCategoryColumn}
                            onChange={(e) => setSelectedCategoryColumn(e.target.value)}
                            className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
                          >
                            {headers.map(header => (
                              <option key={header} value={header} className="bg-slate-800">
                                {header}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Chart Container */}
                      <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
                        <div className="h-64 md:h-80">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={filteredData}>
                              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                              <XAxis
                                dataKey={selectedCategoryColumn}
                                tick={{fill: 'rgba(255,255,255,0.8)', fontSize: 12}}
                                axisLine={{stroke: 'rgba(255,255,255,0.3)'}}
                                tickLine={{stroke: 'rgba(255,255,255,0.3)'}}
                              />
                              <YAxis
                                tick={{fill: 'rgba(255,255,255,0.8)', fontSize: 12}}
                                axisLine={{stroke: 'rgba(255,255,255,0.3)'}}
                                tickLine={{stroke: 'rgba(255,255,255,0.3)'}}
                              />
                              <Tooltip
                                contentStyle={{
                                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                  border: '1px solid rgba(255, 255, 255, 0.2)',
                                  borderRadius: '12px',
                                  color: '#fff',
                                  backdropFilter: 'blur(20px)'
                                }}
                              />
                              <Legend
                                wrapperStyle={{
                                  color: 'rgba(255,255,255,0.8)'
                                }}
                              />
                              <Bar
                                dataKey={selectedNumericColumn}
                                fill="url(#gradient)"
                                animationDuration={1000}
                                radius={[6, 6, 0, 0]}
                              />
                              <defs>
                                <linearGradient id="gradient" x1="0" y1="0" x2="0" y2="1">
                                  <stop offset="0%" stopColor="#a855f7" />
                                  <stop offset="100%" stopColor="#ec4899" />
                                </linearGradient>
                              </defs>
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                      </div>

                      {/* Chart Summary */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-3 text-center">
                          <div className="text-lg font-bold text-white">{filteredData.length}</div>
                          <div className="text-xs text-white/60">Tổng dòng</div>
                        </div>
                        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-3 text-center">
                          <div className="text-lg font-bold text-white">{headers.length}</div>
                          <div className="text-xs text-white/60">Tổng cột</div>
                        </div>
                        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-3 text-center">
                          <div className="text-lg font-bold text-white">{numericColumns.length}</div>
                          <div className="text-xs text-white/60">Cột số</div>
                        </div>
                        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-3 text-center">
                          <div className="text-lg font-bold text-white">{sheets.length}</div>
                          <div className="text-xs text-white/60">Sheets</div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="p-8 text-center">
                      <div className="text-4xl mb-4">📊</div>
                      <div className="text-white/60">
                        Không tìm thấy dữ liệu số để tạo biểu đồ.
                      </div>
                      <div className="text-sm text-white/40 mt-2">
                        Vui lòng đảm bảo bảng tính có ít nhất một cột chứa dữ liệu số.
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </main>

      {/* Bottom Navigation - Mobile */}
      {connected && (
        <div className="fixed bottom-0 left-0 right-0 z-20 md:hidden">
          <div className="bg-white/10 backdrop-blur-xl border-t border-white/20 px-4 py-2">
            <div className="flex justify-around items-center">
              <button
                onClick={() => setActiveView('table')}
                className={`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${
                  activeView === 'table'
                    ? 'text-purple-300 bg-purple-500/20'
                    : 'text-white/60 hover:text-white/80'
                }`}
              >
                <span className="text-lg">📋</span>
                <span className="text-xs">Bảng</span>
              </button>

              <button
                onClick={() => setActiveView('chart')}
                className={`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${
                  activeView === 'chart'
                    ? 'text-purple-300 bg-purple-500/20'
                    : 'text-white/60 hover:text-white/80'
                }`}
              >
                <span className="text-lg">📊</span>
                <span className="text-xs">Biểu đồ</span>
              </button>

              <button
                onClick={() => setDashboardMode(!dashboardMode)}
                className={`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${
                  dashboardMode
                    ? 'text-purple-300 bg-purple-500/20'
                    : 'text-white/60 hover:text-white/80'
                }`}
              >
                <span className="text-lg">🏠</span>
                <span className="text-xs">Dashboard</span>
              </button>

              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex flex-col items-center gap-1 p-2 rounded-xl text-white/60 hover:text-white/80 transition-all duration-200 disabled:opacity-50"
              >
                <span className={`text-lg ${refreshing ? 'animate-spin' : ''}`}>🔄</span>
                <span className="text-xs">Refresh</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Floating Action Button - Desktop */}
      {connected && (
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="hidden md:flex fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full items-center justify-center text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 disabled:opacity-50 z-20"
        >
          <span className={`text-xl ${refreshing ? 'animate-spin' : ''}`}>🔄</span>
        </button>
      )}
    </div>
  );
};

export default EnhancedGoogleSheetsApp;
