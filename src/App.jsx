import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const EnhancedGoogleSheetsApp = () => {
  const [data, setData] = useState([]);
  const [headers, setHeaders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [sheetUrl, setSheetUrl] = useState('');
  const [connected, setConnected] = useState(false);
  const [activeView, setActiveView] = useState('table');
  const [selectedNumericColumn, setSelectedNumericColumn] = useState('');
  const [selectedCategoryColumn, setSelectedCategoryColumn] = useState('');
  const [numericColumns, setNumericColumns] = useState([]);
  const [sheets, setSheets] = useState([]);
  const [selectedSheet, setSelectedSheet] = useState(null);
  const [loadingSheets, setLoadingSheets] = useState(false);

  const parseCSV = (csvText) => {
    const lines = csvText.split('\n');
    const result = [];

    for (let line of lines) {
      if (!line.trim()) continue;

      const row = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          row.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }

      row.push(current.trim());
      result.push(row);
    }

    return result;
  };

  const fetchSheetsMetadata = async (sheetId) => {
    try {
      setLoadingSheets(true);

      // Thử lấy HTML của sheet để parse metadata
      const htmlUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/edit`;
      const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(htmlUrl)}`;

      const response = await fetch(proxyUrl);
      if (!response.ok) {
        return [{ title: 'Sheet1', id: '0' }];
      }

      const data = await response.json();
      const htmlText = data.contents;

      // Parse sheet names từ HTML
      const sheetMatches = htmlText.match(/"sheets":\[([^\]]+)\]/);
      if (sheetMatches) {
        const sheetsData = sheetMatches[1];
        const titleMatches = sheetsData.match(/"title":"([^"]+)"/g);
        const idMatches = sheetsData.match(/"sheetId":(\d+)/g);

        if (titleMatches && idMatches) {
          const extractedSheets = titleMatches.map((titleMatch, index) => {
            const title = titleMatch.match(/"title":"([^"]+)"/)[1];
            const id = idMatches[index] ? idMatches[index].match(/"sheetId":(\d+)/)[1] : '0';
            return { title, id };
          });
          return extractedSheets;
        }
      }

      return [{ title: 'Sheet1', id: '0' }];
    } catch (err) {
      console.warn('Không thể lấy metadata sheets, sử dụng sheet mặc định:', err);
      return [{ title: 'Sheet1', id: '0' }];
    } finally {
      setLoadingSheets(false);
    }
  };

  const fetchGoogleSheetData = async (url, gid = null) => {
    try {
      setLoading(true);
      setError(null);

      const sheetIdMatch = url.match(/[-\w]{25,}/);
      if (!sheetIdMatch) {
        throw new Error('URL Google Sheet không hợp lệ');
      }

      const sheetId = sheetIdMatch[0];
      let csvUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/export?format=csv`;

      if (gid !== null) {
        csvUrl += `&gid=${gid}`;
      }

      let csvText = '';

      try {
        // Thử trực tiếp trước
        const directResponse = await fetch(csvUrl);
        if (directResponse.ok) {
          csvText = await directResponse.text();
        } else {
          throw new Error('Direct fetch failed');
        }
      } catch (directError) {
        // Nếu trực tiếp không được, dùng proxy
        try {
          const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(csvUrl)}`;
          const proxyResponse = await fetch(proxyUrl);
          if (!proxyResponse.ok) {
            throw new Error('Proxy fetch failed');
          }
          const proxyData = await proxyResponse.json();
          csvText = proxyData.contents;
        } catch (proxyError) {
          throw new Error('Không thể kết nối với Google Sheet. Hãy đảm bảo sheet đã được chia sẻ công khai và thử lại.');
        }
      }

      if (!csvText || csvText.trim() === '') {
        throw new Error('Google Sheet trống hoặc không có dữ liệu');
      }

      const rows = parseCSV(csvText);

      if (rows.length === 0) {
        throw new Error('Không tìm thấy dữ liệu trong sheet');
      }

      const extractedHeaders = rows[0].map(header => header.replace(/"/g, '').trim());
      setHeaders(extractedHeaders);

      const extractedData = rows.slice(1)
        .filter(row => row.some(cell => cell && cell.trim()))
        .map(row => {
          const rowData = {};
          row.forEach((cell, index) => {
            const cleanValue = cell.replace(/"/g, '').trim();
            const header = extractedHeaders[index];
            if (header) {
              // Thử convert sang số
              const numValue = parseFloat(cleanValue);
              rowData[header] = !isNaN(numValue) && cleanValue !== ''
                ? numValue
                : cleanValue;
            }
          });
          return rowData;
        });

      setData(extractedData);

      const numerics = extractedHeaders.filter(header =>
        extractedData.length > 0 && typeof extractedData[0][header] === 'number'
      );
      setNumericColumns(numerics);

      if (numerics.length > 0) {
        setSelectedNumericColumn(numerics[0]);
      }

      const firstNonNumeric = extractedHeaders.find(header =>
        !numerics.includes(header)
      );

      if (firstNonNumeric) {
        setSelectedCategoryColumn(firstNonNumeric);
      }

    } catch (err) {
      setError(err.message);
      console.error('Lỗi khi tải Google Sheet:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    if (sheetUrl) {
      const sheetIdMatch = sheetUrl.match(/[-\w]{25,}/);
      if (sheetIdMatch) {
        const sheetId = sheetIdMatch[0];
        const availableSheets = await fetchSheetsMetadata(sheetId);
        setSheets(availableSheets);

        if (availableSheets.length > 0) {
          setSelectedSheet(availableSheets[0]);
          await fetchGoogleSheetData(sheetUrl, availableSheets[0].id);
          setConnected(true);
        }
      }
    }
  };

  const handleSheetChange = async (sheet) => {
    setSelectedSheet(sheet);
    await fetchGoogleSheetData(sheetUrl, sheet.id);
  };

  const getViewClass = (view) => {
    if (view === activeView) {
      return 'opacity-100 transition-opacity duration-300';
    }
    return 'hidden opacity-0 transition-opacity duration-300';
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <header className="p-4 bg-white dark:bg-gray-800 shadow-sm backdrop-blur-lg bg-opacity-80 dark:bg-opacity-80">
        <div className="container mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Google Sheets Web App</h1>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-4">
        {!connected ? (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 max-w-3xl mx-auto mt-8 backdrop-blur-lg bg-opacity-90 dark:bg-opacity-90">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Kết nối với Google Sheet</h2>

            <div className="mb-4">
              <label htmlFor="sheet-url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                URL Google Sheet
              </label>
              <input
                id="sheet-url"
                type="text"
                className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                placeholder="https://docs.google.com/spreadsheets/d/..."
                value={sheetUrl}
                onChange={(e) => setSheetUrl(e.target.value)}
              />
              <div className="mt-2">
                <button
                  onClick={() => setSheetUrl('https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit')}
                  className="text-xs text-blue-500 hover:text-blue-600 underline"
                >
                  Dùng sheet demo (Google Sample Data)
                </button>
              </div>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Đảm bảo Google Sheet của bạn đã được chia sẻ công khai (chế độ "Bất kỳ ai có đường link")
              </p>
            </div>

            <button
              onClick={handleConnect}
              disabled={loading || !sheetUrl}
              className={`px-4 py-2 rounded-lg font-medium text-white transition duration-200 ${
                loading || !sheetUrl
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600 focus:ring-2 focus:ring-blue-300'
              }`}
            >
              {loading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Đang kết nối...
                </span>
              ) : 'Kết nối'}
            </button>

            {error && (
              <div className="mt-4 p-3 bg-red-100 border border-red-200 text-red-700 rounded-lg dark:bg-red-900 dark:text-red-100 dark:border-red-800">
                {error}
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white">Dữ liệu từ Google Sheet</h2>
                {sheets.length > 0 && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Tìm thấy {sheets.length} sheet{sheets.length > 1 ? 's' : ''} • Hiện tại: {selectedSheet?.title}
                  </p>
                )}
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                {sheets.length > 1 && (
                  <div className="flex items-center gap-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Sheet:
                    </label>
                    <select
                      value={selectedSheet?.id || ''}
                      onChange={(e) => {
                        const sheet = sheets.find(s => s.id === e.target.value);
                        if (sheet) handleSheetChange(sheet);
                      }}
                      disabled={loading}
                      className="px-3 py-1.5 text-sm rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                    >
                      {sheets.map(sheet => (
                        <option key={sheet.id} value={sheet.id}>
                          {sheet.title}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <button
                  onClick={() => {
                    setConnected(false);
                    setSheets([]);
                    setSelectedSheet(null);
                    setData([]);
                    setHeaders([]);
                  }}
                  className="px-3 py-1.5 text-sm rounded-lg font-medium text-white bg-blue-500 hover:bg-blue-600 focus:ring-2 focus:ring-blue-300 transition duration-200"
                >
                  Kết nối lại
                </button>
              </div>
            </div>

            {loading && (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-8 text-center backdrop-blur-lg bg-opacity-90 dark:bg-opacity-90">
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="text-gray-700 dark:text-gray-300">
                    {loadingSheets ? 'Đang tải danh sách sheet...' : 'Đang tải dữ liệu...'}
                  </span>
                </div>
              </div>
            )}

            {data.length > 0 && !loading && (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden backdrop-blur-lg bg-opacity-90 dark:bg-opacity-90">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setActiveView('table')}
                      className={`px-3 py-1.5 text-sm rounded-lg font-medium transition duration-200 ${
                        activeView === 'table'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      Bảng dữ liệu
                    </button>
                    <button
                      onClick={() => setActiveView('chart')}
                      className={`px-3 py-1.5 text-sm rounded-lg font-medium transition duration-200 ${
                        activeView === 'chart'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      Biểu đồ
                    </button>
                  </div>
                </div>

                <div className={getViewClass('table')}>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          {headers.map((header, index) => (
                            <th
                              key={index}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                            >
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {data.map((row, rowIndex) => (
                          <tr key={rowIndex} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                            {headers.map((header, cellIndex) => (
                              <td
                                key={cellIndex}
                                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
                              >
                                {row[header]}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className={getViewClass('chart')}>
                  {numericColumns.length > 0 ? (
                    <div className="p-4">
                      <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Chọn trường dữ liệu
                          </label>
                          <select
                            value={selectedNumericColumn}
                            onChange={(e) => setSelectedNumericColumn(e.target.value)}
                            className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                          >
                            {numericColumns.map(column => (
                              <option key={column} value={column}>{column}</option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Phân loại theo
                          </label>
                          <select
                            value={selectedCategoryColumn}
                            onChange={(e) => setSelectedCategoryColumn(e.target.value)}
                            className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                          >
                            {headers.map(header => (
                              <option key={header} value={header}>{header}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={data}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#555" />
                            <XAxis
                              dataKey={selectedCategoryColumn}
                              tick={{fill: '#999'}}
                              axisLine={{stroke: '#555'}}
                            />
                            <YAxis
                              tick={{fill: '#999'}}
                              axisLine={{stroke: '#555'}}
                            />
                            <Tooltip
                              contentStyle={{
                                backgroundColor: 'rgba(50, 50, 50, 0.8)',
                                border: 'none',
                                borderRadius: '8px',
                                color: '#fff'
                              }}
                            />
                            <Legend />
                            <Bar
                              dataKey={selectedNumericColumn}
                              fill="#3b82f6"
                              animationDuration={1000}
                              radius={[4, 4, 0, 0]}
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  ) : (
                    <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                      Không tìm thấy dữ liệu số để tạo biểu đồ. Vui lòng đảm bảo bảng tính của bạn có ít nhất một cột chứa dữ liệu số.
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </main>

      <footer className="p-4 bg-white dark:bg-gray-800 shadow-sm mt-auto backdrop-blur-lg bg-opacity-80 dark:bg-opacity-80">
        <div className="container mx-auto text-center text-sm text-gray-500 dark:text-gray-400">
          &copy; {new Date().getFullYear()} Google Sheets Web App
        </div>
      </footer>
    </div>
  );
};

export default EnhancedGoogleSheetsApp;
