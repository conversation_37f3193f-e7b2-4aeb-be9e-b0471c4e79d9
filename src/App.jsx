import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const EnhancedGoogleSheetsApp = () => {
  const [data, setData] = useState([]);
  const [headers, setHeaders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [sheetUrl, setSheetUrl] = useState('https://docs.google.com/spreadsheets/d/1puKECNQhgaRs9-d7nqHH6U9qTvGiWlchEes9CCpHCbI/edit?gid=394890302#gid=394890302');
  const [connected, setConnected] = useState(false);
  const [activeView, setActiveView] = useState('cards');
  const [selectedNumericColumn, setSelectedNumericColumn] = useState('');
  const [selectedCategoryColumn, setSelectedCategoryColumn] = useState('');
  const [numericColumns, setNumericColumns] = useState([]);
  const [sheets, setSheets] = useState([]);
  const [selectedSheet, setSelectedSheet] = useState(null);
  const [loadingSheets, setLoadingSheets] = useState(false);
  const [debugInfo, setDebugInfo] = useState('');
  const [lastSyncTime, setLastSyncTime] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [filteredData, setFilteredData] = useState([]);
  const [dashboardMode, setDashboardMode] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Auto connect khi load trang
  useEffect(() => {
    if (sheetUrl && !connected) {
      console.log('Auto connecting...');
      handleConnect();
    }
  }, []);

  // Real-time sync every 60 seconds
  useEffect(() => {
    if (connected && selectedSheet) {
      const interval = setInterval(() => {
        console.log('Auto refreshing data...');
        handleRefresh();
      }, 60000); // 60 seconds

      return () => clearInterval(interval);
    }
  }, [connected, selectedSheet]);

  // Filter data based on search query
  useEffect(() => {
    if (!data.length) {
      setFilteredData([]);
      return;
    }

    let filtered = data;
    
    if (searchQuery.trim()) {
      filtered = data.filter(row =>
        Object.values(row).some(value =>
          value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Apply sorting
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        const aVal = a[sortConfig.key];
        const bVal = b[sortConfig.key];
        
        if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    setFilteredData(filtered);
  }, [data, searchQuery, sortConfig]);

  const parseCSV = (csvText) => {
    try {
      const lines = csvText.split('\n').filter(line => line.trim());
      const result = [];
      
      for (let line of lines) {
        const row = line.split(',').map(cell => {
          let cleaned = cell.trim();
          if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
            cleaned = cleaned.slice(1, -1);
          }
          return cleaned;
        });
        
        result.push(row);
      }
      
      console.log('CSV parsing result:', result.length, 'rows');
      return result;
    } catch (error) {
      console.error('CSV parsing error:', error);
      throw new Error('Lỗi khi phân tích dữ liệu CSV');
    }
  };

  const detectSheetsByTesting = async (sheetId, originalUrl = '') => {
    console.log('Testing different GIDs to detect sheets...');
    const detectedSheets = [];
    
    const knownGids = [
      { gid: '394890302', name: 'Ninh' },
      { gid: '1715384629', name: 'Mr Hùng' }
    ];
    
    const urlGidMatch = originalUrl.match(/[#&]gid=(\d+)/);
    const urlGid = urlGidMatch ? urlGidMatch[1] : null;
    
    const gidsToTest = [];
    
    if (urlGid) {
      const knownSheet = knownGids.find(sheet => sheet.gid === urlGid);
      if (knownSheet) {
        gidsToTest.push({ gid: urlGid, name: knownSheet.name });
      } else {
        gidsToTest.push({ gid: urlGid, name: 'Sheet hiện tại' });
      }
      console.log('Found GID in URL:', urlGid);
    }
    
    knownGids.forEach(sheet => {
      if (!gidsToTest.find(g => g.gid === sheet.gid)) {
        gidsToTest.push(sheet);
      }
    });
    
    ['0', '1', '2', '3', '4', '5'].forEach(gid => {
      if (!gidsToTest.find(g => g.gid === gid)) {
        gidsToTest.push({ gid, name: `Sheet${parseInt(gid) + 1}` });
      }
    });

    for (const sheetInfo of gidsToTest) {
      try {
        const gid = sheetInfo.gid;
        const sheetName = sheetInfo.name;
        
        console.log(`Testing GID ${gid} (${sheetName})...`);
        setDebugInfo && setDebugInfo(`Đang test ${sheetName}...`);
        
        const testUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/export?format=csv&gid=${gid}`;
        
        let response;
        try {
          response = await fetch(testUrl);
        } catch (directError) {
          const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(testUrl)}`;
          response = await fetch(proxyUrl);
        }
        
        if (response.ok) {
          const csvData = await response.text();
          console.log(`GID ${gid} (${sheetName}) response length:`, csvData.length);
          
          if (csvData && csvData.trim().length > 10) {
            detectedSheets.push({
              title: sheetName,
              id: gid
            });
            
            console.log(`✓ Found sheet with GID ${gid}: ${sheetName}`);
          } else {
            console.log(`✗ GID ${gid} (${sheetName}) has no data`);
          }
        } else {
          console.log(`✗ GID ${gid} (${sheetName}) failed with status:`, response.status);
        }
      } catch (error) {
        console.log(`✗ GID ${sheetInfo.gid} (${sheetInfo.name}) error:`, error.message);
      }
      
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    console.log('Detection complete. Found sheets:', detectedSheets);
    return detectedSheets;
  };

  const fetchSheetsMetadata = async (sheetId) => {
    try {
      setLoadingSheets(true);
      console.log('Fetching metadata for sheet ID:', sheetId);
      
      const detectedSheets = await detectSheetsByTesting(sheetId, '');
      
      if (detectedSheets.length > 0) {
        console.log('Successfully detected sheets:', detectedSheets);
        return detectedSheets;
      }
      
      console.log('No sheets detected, using fallback');
      return [{ title: 'Sheet1', id: '0' }];
    } catch (err) {
      console.warn('Không thể lấy metadata sheets, sử dụng sheet mặc định:', err);
      return [{ title: 'Sheet1', id: '0' }];
    } finally {
      setLoadingSheets(false);
    }
  };

  const fetchGoogleSheetData = async (url, gid = null) => {
    try {
      console.log('Fetching data for URL:', url, 'GID:', gid);
      
      const sheetIdMatch = url.match(/[-\w]{25,}/);
      if (!sheetIdMatch) {
        throw new Error('URL Google Sheet không hợp lệ');
      }
      
      const sheetId = sheetIdMatch[0];
      let csvUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/export?format=csv`;
      
      if (gid !== null && gid !== '0') {
        csvUrl += `&gid=${gid}`;
      }
      
      console.log('CSV URL:', csvUrl);
      let csvText = '';
      
      try {
        console.log('Trying direct fetch...');
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const directResponse = await fetch(csvUrl, { 
          signal: controller.signal,
          mode: 'cors'
        });
        clearTimeout(timeoutId);
        console.log('Direct response status:', directResponse.status);
        
        if (directResponse.ok) {
          csvText = await directResponse.text();
          console.log('Direct fetch successful, data length:', csvText.length);
        } else {
          throw new Error(`Direct fetch failed with status: ${directResponse.status}`);
        }
      } catch (directError) {
        console.log('Direct fetch failed:', directError.message);
        try {
          console.log('Trying faster proxy...');
          const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(csvUrl)}`;
          const proxyResponse = await fetch(proxyUrl, {
            headers: {
              'Accept': 'text/csv,text/plain,*/*'
            }
          });
          console.log('Proxy response status:', proxyResponse.status);
          
          if (!proxyResponse.ok) {
            throw new Error(`Proxy fetch failed with status: ${proxyResponse.status}`);
          }
          csvText = await proxyResponse.text();
          console.log('Proxy fetch successful, data length:', csvText.length);
        } catch (proxyError) {
          console.error('Proxy fetch failed:', proxyError);
          try {
            console.log('Trying backup proxy...');
            const backupProxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(csvUrl)}`;
            const backupResponse = await fetch(backupProxyUrl);
            if (backupResponse.ok) {
              const backupData = await backupResponse.json();
              csvText = backupData.contents;
              console.log('Backup proxy successful, data length:', csvText.length);
            } else {
              throw new Error('All proxies failed');
            }
          } catch (backupError) {
            throw new Error('Không thể kết nối với Google Sheet. Hãy đảm bảo sheet đã được chia sẻ công khai và thử lại.');
          }
        }
      }
      
      if (!csvText || csvText.trim() === '') {
        throw new Error('Google Sheet trống hoặc không có dữ liệu');
      }
      
      console.log('CSV text preview:', csvText.substring(0, 500));
      
      const rows = parseCSV(csvText);
      console.log('Parsed rows:', rows.length, 'First row:', rows[0]);
      
      if (rows.length === 0) {
        throw new Error('Không tìm thấy dữ liệu trong sheet');
      }
      
      const extractedHeaders = rows[0].map(header => header.replace(/"/g, '').trim());
      console.log('Headers:', extractedHeaders);
      setHeaders(extractedHeaders);
      
      const extractedData = rows.slice(1)
        .filter(row => row.some(cell => cell && cell.trim()))
        .map(row => {
          const rowData = {};
          row.forEach((cell, index) => {
            const cleanValue = cell.replace(/"/g, '').trim();
            const header = extractedHeaders[index];
            if (header) {
              const numValue = parseFloat(cleanValue);
              rowData[header] = !isNaN(numValue) && cleanValue !== '' 
                ? numValue 
                : cleanValue;
            }
          });
          return rowData;
        });
      
      console.log('Extracted data:', extractedData.length, 'rows');
      console.log('First data row:', extractedData[0]);
      setData(extractedData);
      
      const numerics = extractedHeaders.filter(header => 
        extractedData.length > 0 && typeof extractedData[0][header] === 'number'
      );
      console.log('Numeric columns:', numerics);
      setNumericColumns(numerics);
      
      if (numerics.length > 0) {
        setSelectedNumericColumn(numerics[0]);
      }
      
      const firstNonNumeric = extractedHeaders.find(header => 
        !numerics.includes(header)
      );
      
      if (firstNonNumeric) {
        setSelectedCategoryColumn(firstNonNumeric);
      }
      
      console.log('Data processing completed successfully');

    } catch (err) {
      setError(err.message);
      console.error('Lỗi khi tải Google Sheet:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    if (!sheetUrl) {
      setError('Vui lòng nhập URL Google Sheet');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setDebugInfo('Bắt đầu kết nối...');
      console.log('Bắt đầu kết nối với:', sheetUrl);

      const sheetIdMatch = sheetUrl.match(/[-\w]{25,}/);
      if (!sheetIdMatch) {
        throw new Error('URL Google Sheet không hợp lệ');
      }

      const sheetId = sheetIdMatch[0];
      console.log('Sheet ID:', sheetId);
      setDebugInfo(`Sheet ID: ${sheetId}`);

      setDebugInfo('Đang tải dữ liệu...');

      const [dataResult, metaResult] = await Promise.allSettled([
        fetchGoogleSheetData(sheetUrl, null),
        detectSheetsByTesting(sheetId, sheetUrl)
      ]);

      if (dataResult.status === 'rejected') {
        throw dataResult.reason;
      }

      if (metaResult.status === 'fulfilled') {
        const detectedSheets = metaResult.value;
        console.log('Final detected sheets:', detectedSheets);
        setSheets(detectedSheets);
        setSelectedSheet(detectedSheets[0]);
        setDebugInfo(`Phát hiện ${detectedSheets.length} sheet(s)`);
      } else {
        console.warn('Không thể detect sheets:', metaResult.reason);
        setSheets([{ title: 'Sheet1', id: '0' }]);
        setSelectedSheet({ title: 'Sheet1', id: '0' });
        setDebugInfo('Sử dụng sheet mặc định');
      }

      setConnected(true);
      setLastSyncTime(new Date());
      setDebugInfo('Kết nối thành công!');
      console.log('Kết nối thành công!');

    } catch (err) {
      console.error('Lỗi kết nối:', err);
      setError(err.message);
      setDebugInfo(`Lỗi: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSheetChange = async (sheet) => {
    setSelectedSheet(sheet);
    await fetchGoogleSheetData(sheetUrl, sheet.id);
    setLastSyncTime(new Date());
  };

  const handleRefresh = async () => {
    if (!selectedSheet) return;

    setRefreshing(true);
    try {
      await fetchGoogleSheetData(sheetUrl, selectedSheet.id);
      setLastSyncTime(new Date());
      setDebugInfo('Đã cập nhật dữ liệu');
    } catch (error) {
      setError('Lỗi khi cập nhật: ' + error.message);
    } finally {
      setRefreshing(false);
    }
  };

  const handleSort = (columnKey) => {
    let direction = 'asc';
    if (sortConfig.key === columnKey && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key: columnKey, direction });
  };

  const formatLastSync = () => {
    if (!lastSyncTime) return '';
    const now = new Date();
    const diff = Math.floor((now - lastSyncTime) / 1000);

    if (diff < 60) return `${diff}s trước`;
    if (diff < 3600) return `${Math.floor(diff / 60)}m trước`;
    return `${Math.floor(diff / 3600)}h trước`;
  };

  const getViewClass = (view) => {
    if (view === activeView) {
      return 'opacity-100 transition-opacity duration-300';
    }
    return 'hidden opacity-0 transition-opacity duration-300';
  };

  return (
    <div className="min-h-screen" style={{
      background: `
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #16213e 100%)
      `
    }}>
      {/* iOS 18 Dynamic Island Background Effects */}
      <div className="fixed inset-0 z-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"
             style={{ animationDuration: '4s' }}></div>
        <div className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-bl from-pink-500/20 to-orange-500/20 rounded-full blur-3xl animate-pulse"
             style={{ animationDuration: '6s', animationDelay: '2s' }}></div>
        <div className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-tr from-teal-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse"
             style={{ animationDuration: '5s', animationDelay: '1s' }}></div>

        <div className="absolute top-20 left-20 w-4 h-4 bg-white/30 rounded-full animate-bounce"
             style={{ animationDuration: '3s', animationDelay: '0s' }}></div>
        <div className="absolute top-40 right-32 w-3 h-3 bg-blue-400/40 rounded-full animate-bounce"
             style={{ animationDuration: '4s', animationDelay: '1s' }}></div>
        <div className="absolute bottom-32 left-40 w-2 h-2 bg-purple-400/50 rounded-full animate-bounce"
             style={{ animationDuration: '3.5s', animationDelay: '2s' }}></div>
        <div className="absolute bottom-20 right-20 w-3 h-3 bg-pink-400/40 rounded-full animate-bounce"
             style={{ animationDuration: '4.5s', animationDelay: '0.5s' }}></div>

        <div className="absolute inset-0 opacity-30">
          <div className="w-full h-full bg-gradient-to-br from-transparent via-white/5 to-transparent"></div>
        </div>
      </div>

      {/* iOS 18 Style Navigation Bar */}
      <nav className="relative z-10 ios-glass-regular border-b border-white/10 sticky top-0"
           style={{ animation: 'ios-slide-up 0.6s ease-out' }}>
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4" style={{ animation: 'ios-spring-in 0.8s ease-out' }}>
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-2xl">📊</span>
                </div>
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                  <span className="text-xs">✓</span>
                </div>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  Sheets Analytics
                </h1>
                <p className="text-sm text-white/60">Real-time Dashboard</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {lastSyncTime && (
                <div className="ios-glass-thin px-4 py-2 rounded-full flex items-center gap-2"
                     style={{ animation: 'ios-fade-in 1s ease-out 0.3s both' }}>
                  <div className={`w-2 h-2 rounded-full ${refreshing ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'}`}></div>
                  <span className="text-xs text-white/80 font-medium">
                    {refreshing ? 'Syncing...' : `${formatLastSync()}`}
                  </span>
                </div>
              )}

              {sheets.length > 1 && (
                <div className="ios-glass-thin px-3 py-2 rounded-full"
                     style={{ animation: 'ios-fade-in 1s ease-out 0.5s both' }}>
                  <span className="text-xs text-white/80 font-medium">
                    {sheets.length} Sheets
                  </span>
                </div>
              )}
            </div>
          </div>

          {(loading || refreshing) && (
            <div className="mt-4 w-full h-1 bg-white/10 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                   style={{
                     animation: 'ios-shimmer 2s infinite',
                     background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
                     backgroundSize: '200% 100%'
                   }}></div>
            </div>
          )}
        </div>
      </nav>

      <main className="relative z-10 flex-grow pb-20">
        {!connected ? (
          <div className="max-w-2xl mx-auto px-6 py-16">
            <div className="ios-card p-8 text-center" style={{ animation: 'ios-spring-in 1s ease-out' }}>
              <div className="relative mb-8">
                <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500 rounded-3xl flex items-center justify-center shadow-2xl"
                     style={{ animation: 'ios-glow 3s ease-in-out infinite' }}>
                  <span className="text-4xl">🚀</span>
                </div>
                <div className="absolute -top-2 -right-8 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
                     style={{ animation: 'ios-bounce 2s ease-in-out infinite' }}>
                  <span className="text-sm">✨</span>
                </div>
              </div>

              <h2 className="text-3xl font-bold mb-3 text-white">
                Sheets Analytics
              </h2>
              <p className="text-white/70 mb-8 text-lg leading-relaxed">
                Transform your Google Sheets data into beautiful, interactive dashboards
              </p>

              {loading && (
                <div className="ios-glass-thin p-6 rounded-2xl mb-6" style={{ animation: 'ios-slide-up 0.5s ease-out' }}>
                  <div className="flex items-center justify-center gap-4 mb-6">
                    <div className="relative">
                      <div className="w-10 h-10 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                      <div className="absolute inset-0 w-10 h-10 border-2 border-blue-400/20 rounded-full"></div>
                    </div>
                    <div>
                      <div className="text-white font-semibold">Connecting to Sheets</div>
                      <div className="text-white/60 text-sm">Establishing secure connection</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-white/80 text-sm">Authenticating with Google</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                      <span className="text-white/80 text-sm">Discovering sheets</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                      <span className="text-white/80 text-sm">Processing data</span>
                    </div>
                  </div>
                </div>
              )}

              {error && (
                <div className="ios-glass-thin p-6 rounded-2xl border border-red-400/30 mb-6" style={{ animation: 'ios-slide-up 0.5s ease-out' }}>
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
                      <span className="text-red-400">⚠️</span>
                    </div>
                    <div className="text-left">
                      <div className="text-red-200 font-medium">Connection Error</div>
                      <div className="text-red-300/60 text-sm">Please check your connection</div>
                    </div>
                  </div>
                  <div className="text-red-100 text-sm bg-red-500/10 p-3 rounded-xl">
                    {error}
                  </div>
                </div>
              )}

              {debugInfo && !loading && (
                <div className="ios-glass-thin p-4 rounded-2xl border border-blue-400/30 mb-6" style={{ animation: 'ios-slide-up 0.5s ease-out' }}>
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center">
                      <span className="text-blue-400 text-sm">🔧</span>
                    </div>
                    <span className="text-blue-200 font-medium text-sm">Debug Info</span>
                  </div>
                  <div className="text-blue-100/80 text-xs bg-blue-500/10 p-3 rounded-xl font-mono">
                    {debugInfo}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 mt-6">
                <div className="ios-card p-4 rounded-xl" style={{ animation: 'ios-slide-up 0.6s ease-out 0.2s both' }}>
                  <div className="text-2xl mb-2">📊</div>
                  <div className="text-white/80 text-sm font-medium">Real-time</div>
                  <div className="text-white/50 text-xs">Live data sync</div>
                </div>
                <div className="ios-card p-4 rounded-xl" style={{ animation: 'ios-slide-up 0.6s ease-out 0.4s both' }}>
                  <div className="text-2xl mb-2">🔍</div>
                  <div className="text-white/80 text-sm font-medium">Smart Search</div>
                  <div className="text-white/50 text-xs">Advanced filtering</div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="max-w-7xl mx-auto px-6 py-8 space-y-8">
            <div className="ios-card p-6" style={{ animation: 'ios-spring-in 0.8s ease-out' }}>
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="w-14 h-14 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                      <span className="text-2xl">📊</span>
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold">✓</span>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-2xl font-bold text-white mb-1">
                      Analytics Dashboard
                    </h2>
                    {sheets.length > 0 && (
                      <div className="flex items-center gap-3 text-sm text-white/70">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                          <span>{sheets.length} sheet{sheets.length > 1 ? 's' : ''} connected</span>
                        </div>
                        <span className="text-white/40">•</span>
                        <span className="text-blue-300 font-medium">{selectedSheet?.title}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {sheets.length > 1 && (
                    <div className="ios-glass-thin px-4 py-2 rounded-full">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                        <span className="text-emerald-300 text-sm font-medium">Multi-Sheet</span>
                      </div>
                    </div>
                  )}

                  <button
                    onClick={handleRefresh}
                    disabled={refreshing}
                    className="ios-button w-12 h-12 rounded-xl disabled:opacity-50"
                  >
                    <span className={`text-lg transition-transform duration-300 ${refreshing ? 'animate-spin' : ''}`}>
                      🔄
                    </span>
                  </button>

                  <button className="ios-button w-12 h-12 rounded-xl">
                    <span className="text-lg">⚙️</span>
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
                <div className="ios-card p-5 text-center" style={{ animation: 'ios-spring-in 0.6s ease-out 0.1s both' }}>
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl">📄</span>
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">{filteredData.length}</div>
                  <div className="text-sm text-white/60 mb-3">Records</div>
                  <div className="w-full h-2 bg-white/10 rounded-full overflow-hidden">
                    <div className="h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded-full transition-all duration-1000"
                         style={{width: '85%'}}></div>
                  </div>
                </div>

                <div className="ios-card p-5 text-center" style={{ animation: 'ios-spring-in 0.6s ease-out 0.2s both' }}>
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl">📊</span>
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">{headers.length}</div>
                  <div className="text-sm text-white/60 mb-3">Columns</div>
                  <div className="w-full h-2 bg-white/10 rounded-full overflow-hidden">
                    <div className="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all duration-1000"
                         style={{width: '70%'}}></div>
                  </div>
                </div>

                <div className="ios-card p-5 text-center" style={{ animation: 'ios-spring-in 0.6s ease-out 0.3s both' }}>
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl">🔢</span>
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">{numericColumns.length}</div>
                  <div className="text-sm text-white/60 mb-3">Numeric</div>
                  <div className="w-full h-2 bg-white/10 rounded-full overflow-hidden">
                    <div className="h-full bg-gradient-to-r from-orange-400 to-orange-600 rounded-full transition-all duration-1000"
                         style={{width: '60%'}}></div>
                  </div>
                </div>

                <div className="ios-card p-5 text-center" style={{ animation: 'ios-spring-in 0.6s ease-out 0.4s both' }}>
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl">📋</span>
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">{sheets.length}</div>
                  <div className="text-sm text-white/60 mb-3">Sheets</div>
                  <div className="w-full h-2 bg-white/10 rounded-full overflow-hidden">
                    <div className="h-full bg-gradient-to-r from-purple-400 to-purple-600 rounded-full transition-all duration-1000"
                         style={{width: '100%'}}></div>
                  </div>
                </div>
              </div>
            </div>

            {/* iOS 18 Search & Controls */}
            <div className="ios-card p-6" style={{ animation: 'ios-slide-up 0.6s ease-out 0.2s both' }}>
              <div className="space-y-6">
                <div>
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                      <span className="text-lg">🔍</span>
                    </div>
                    <h3 className="text-xl font-semibold text-white">Search & Filter</h3>
                  </div>

                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search across all data..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full px-6 py-4 ios-glass-thin rounded-2xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-300 text-lg"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                      {searchQuery && (
                        <button
                          onClick={() => setSearchQuery('')}
                          className="w-8 h-8 ios-glass-thin rounded-full flex items-center justify-center text-white/60 hover:text-white transition-all duration-200"
                        >
                          ✕
                        </button>
                      )}
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-sm">⚡</span>
                      </div>
                    </div>
                  </div>

                  {searchQuery && (
                    <div className="flex items-center gap-3 mt-3 text-sm" style={{ animation: 'ios-fade-in 0.3s ease-out' }}>
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-white/70">
                        Found <span className="text-green-400 font-semibold">{filteredData.length}</span> results
                      </span>
                    </div>
                  )}
                </div>

                <div className="flex flex-wrap items-center gap-4">
                  {sheets.length > 0 && (
                    <div className="flex items-center gap-3">
                      <label className="text-sm font-medium text-white/70">Sheet:</label>
                      <div className="relative">
                        <select
                          value={selectedSheet?.id || ''}
                          onChange={(e) => {
                            const sheet = sheets.find(s => s.id === e.target.value);
                            if (sheet) handleSheetChange(sheet);
                          }}
                          disabled={loading}
                          className="ios-button px-4 py-2 rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400 disabled:opacity-50 appearance-none pr-8 min-w-32"
                        >
                          {sheets.map(sheet => (
                            <option key={sheet.id} value={sheet.id} className="bg-slate-800">
                              {sheet.title}
                            </option>
                          ))}
                        </select>
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
                          <span className="text-white/60">▼</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center gap-2 ios-card p-1 rounded-xl">
                    <button
                      onClick={() => setActiveView('cards')}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                        activeView === 'cards'
                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                          : 'text-white/70 hover:text-white hover:bg-white/10'
                      }`}
                    >
                      📱 Cards
                    </button>
                    <button
                      onClick={() => setActiveView('table')}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                        activeView === 'table'
                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                          : 'text-white/70 hover:text-white hover:bg-white/10'
                      }`}
                    >
                      📋 Table
                    </button>
                    <button
                      onClick={() => setActiveView('chart')}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                        activeView === 'chart'
                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                          : 'text-white/70 hover:text-white hover:bg-white/10'
                      }`}
                    >
                      📊 Charts
                    </button>
                  </div>

                  <button
                    onClick={() => setDashboardMode(!dashboardMode)}
                    className={`ios-button px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105 ${
                      dashboardMode
                        ? 'bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg'
                        : 'text-white/80 hover:text-white'
                    }`}
                  >
                    {dashboardMode ? '🏠 Dashboard ON' : '🏠 Dashboard'}
                  </button>

                  <div className="flex items-center gap-2 ml-auto">
                    <button className="ios-button p-2 rounded-xl hover:scale-105 transition-all duration-200">
                      <span className="text-white/70 hover:text-white">📤</span>
                    </button>
                    <button className="ios-button p-2 rounded-xl hover:scale-105 transition-all duration-200">
                      <span className="text-white/70 hover:text-white">📥</span>
                    </button>
                    <button className="ios-button p-2 rounded-xl hover:scale-105 transition-all duration-200">
                      <span className="text-white/70 hover:text-white">⚙️</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {loading && (
              <div className="ios-card p-8 text-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="w-8 h-8 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-white/80">
                    {debugInfo || 'Đang tải...'}
                  </span>
                </div>
              </div>
            )}

            {filteredData.length > 0 && !loading && (
              <div className="space-y-6">
                <div className="ios-card p-4" style={{ animation: 'ios-slide-up 0.6s ease-out 0.4s both' }}>
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-white">Data View</h3>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setActiveView('cards')}
                        className={`ios-button px-4 py-2 ${
                          activeView === 'cards' ? 'ios-button-primary' : ''
                        }`}
                      >
                        📱 Cards
                      </button>
                      <button
                        onClick={() => setActiveView('table')}
                        className={`ios-button px-4 py-2 ${
                          activeView === 'table' ? 'ios-button-primary' : ''
                        }`}
                      >
                        📋 Table
                      </button>
                      <button
                        onClick={() => setActiveView('chart')}
                        className={`ios-button px-4 py-2 ${
                          activeView === 'chart' ? 'ios-button-primary' : ''
                        }`}
                      >
                        📊 Charts
                      </button>
                    </div>
                  </div>
                </div>

                <div className={getViewClass('cards')}>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredData.map((row, rowIndex) => (
                      <div key={rowIndex} className="ios-card p-5 hover:scale-105 transition-all duration-300"
                           style={{ animation: `ios-spring-in 0.6s ease-out ${rowIndex * 0.1}s both` }}>
                        <div className="flex items-center justify-between mb-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                            <span className="text-lg">📄</span>
                          </div>
                          <div className="text-xs text-white/50 font-mono">#{rowIndex + 1}</div>
                        </div>

                        <div className="space-y-3">
                          {headers.slice(0, 4).map((header, cellIndex) => (
                            <div key={cellIndex} className="flex flex-col gap-1">
                              <div className="text-xs text-white/60 font-medium uppercase tracking-wide">
                                {header}
                              </div>
                              <div className="text-sm text-white font-medium truncate" title={row[header]}>
                                {row[header] || '—'}
                              </div>
                            </div>
                          ))}

                          {headers.length > 4 && (
                            <div className="pt-2 border-t border-white/10">
                              <div className="text-xs text-white/40">
                                +{headers.length - 4} more fields
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="mt-4 pt-3 border-t border-white/10 flex justify-end">
                          <button className="ios-button px-3 py-1 text-xs">
                            View Details
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className={getViewClass('table')}>
                  <div className="ios-card overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className="min-w-full">
                        <thead className="ios-glass-thin">
                          <tr>
                            {headers.map((header, index) => (
                              <th
                                key={index}
                                onClick={() => handleSort(header)}
                                className="px-6 py-4 text-left text-xs font-semibold text-white/80 uppercase tracking-wider cursor-pointer hover:text-white transition-colors duration-200"
                              >
                                <div className="flex items-center gap-2">
                                  {header}
                                  {sortConfig.key === header && (
                                    <span className="text-blue-400">
                                      {sortConfig.direction === 'asc' ? '↑' : '↓'}
                                    </span>
                                  )}
                                </div>
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {filteredData.map((row, rowIndex) => (
                            <tr key={rowIndex} className="border-b border-white/5 hover:bg-white/5 transition-colors duration-200">
                              {headers.map((header, cellIndex) => (
                                <td
                                  key={cellIndex}
                                  className="px-6 py-4 text-sm text-white/90 max-w-xs truncate"
                                  title={row[header]}
                                >
                                  {row[header] || '—'}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div className={getViewClass('chart')}>
                  {numericColumns.length > 0 ? (
                    <div className="ios-card p-6 space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-white/80 mb-2">
                            📊 Data Field
                          </label>
                          <select
                            value={selectedNumericColumn}
                            onChange={(e) => setSelectedNumericColumn(e.target.value)}
                            className="w-full px-3 py-2 ios-glass-thin rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
                          >
                            {numericColumns.map(column => (
                              <option key={column} value={column} className="bg-slate-800">
                                {column}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-white/80 mb-2">
                            🏷️ Category
                          </label>
                          <select
                            value={selectedCategoryColumn}
                            onChange={(e) => setSelectedCategoryColumn(e.target.value)}
                            className="w-full px-3 py-2 ios-glass-thin rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
                          >
                            {headers.map(header => (
                              <option key={header} value={header} className="bg-slate-800">
                                {header}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="ios-glass-thin p-4 rounded-xl">
                        <div className="h-64 md:h-80">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={filteredData}>
                              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                              <XAxis
                                dataKey={selectedCategoryColumn}
                                tick={{fill: 'rgba(255,255,255,0.8)', fontSize: 12}}
                                axisLine={{stroke: 'rgba(255,255,255,0.3)'}}
                                tickLine={{stroke: 'rgba(255,255,255,0.3)'}}
                              />
                              <YAxis
                                tick={{fill: 'rgba(255,255,255,0.8)', fontSize: 12}}
                                axisLine={{stroke: 'rgba(255,255,255,0.3)'}}
                                tickLine={{stroke: 'rgba(255,255,255,0.3)'}}
                              />
                              <Tooltip
                                contentStyle={{
                                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                  border: '1px solid rgba(255, 255, 255, 0.2)',
                                  borderRadius: '12px',
                                  color: '#fff',
                                  backdropFilter: 'blur(20px)'
                                }}
                              />
                              <Legend
                                wrapperStyle={{
                                  color: 'rgba(255,255,255,0.8)'
                                }}
                              />
                              <Bar
                                dataKey={selectedNumericColumn}
                                fill="url(#gradient)"
                                animationDuration={1000}
                                radius={[6, 6, 0, 0]}
                              />
                              <defs>
                                <linearGradient id="gradient" x1="0" y1="0" x2="0" y2="1">
                                  <stop offset="0%" stopColor="#a855f7" />
                                  <stop offset="100%" stopColor="#ec4899" />
                                </linearGradient>
                              </defs>
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div className="ios-glass-thin p-3 rounded-xl text-center">
                          <div className="text-lg font-bold text-white">{filteredData.length}</div>
                          <div className="text-xs text-white/60">Total Rows</div>
                        </div>
                        <div className="ios-glass-thin p-3 rounded-xl text-center">
                          <div className="text-lg font-bold text-white">{headers.length}</div>
                          <div className="text-xs text-white/60">Total Columns</div>
                        </div>
                        <div className="ios-glass-thin p-3 rounded-xl text-center">
                          <div className="text-lg font-bold text-white">{numericColumns.length}</div>
                          <div className="text-xs text-white/60">Numeric Columns</div>
                        </div>
                        <div className="ios-glass-thin p-3 rounded-xl text-center">
                          <div className="text-lg font-bold text-white">{sheets.length}</div>
                          <div className="text-xs text-white/60">Sheets</div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="ios-card p-8 text-center">
                      <div className="text-4xl mb-4">📊</div>
                      <div className="text-white/60">
                        No numeric data found for charts.
                      </div>
                      <div className="text-sm text-white/40 mt-2">
                        Please ensure your sheet has at least one numeric column.
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </main>

      {connected && (
        <div className="fixed bottom-0 left-0 right-0 z-20 md:hidden">
          <div className="ios-glass-thick border-t border-white/10 px-4 py-3 safe-area-bottom">
            <div className="flex justify-around items-center">
              <button
                onClick={() => setActiveView('cards')}
                className={`flex flex-col items-center gap-1 p-3 rounded-2xl transition-all duration-300 ${
                  activeView === 'cards'
                    ? 'bg-blue-500/30 text-blue-200'
                    : 'text-white/60 hover:text-white/80 hover:bg-white/10'
                }`}
              >
                <span className="text-xl">📱</span>
                <span className="text-xs font-medium">Cards</span>
              </button>

              <button
                onClick={() => setActiveView('table')}
                className={`flex flex-col items-center gap-1 p-3 rounded-2xl transition-all duration-300 ${
                  activeView === 'table'
                    ? 'bg-blue-500/30 text-blue-200'
                    : 'text-white/60 hover:text-white/80 hover:bg-white/10'
                }`}
              >
                <span className="text-xl">📋</span>
                <span className="text-xs font-medium">Table</span>
              </button>

              <button
                onClick={() => setActiveView('chart')}
                className={`flex flex-col items-center gap-1 p-3 rounded-2xl transition-all duration-300 ${
                  activeView === 'chart'
                    ? 'bg-blue-500/30 text-blue-200'
                    : 'text-white/60 hover:text-white/80 hover:bg-white/10'
                }`}
              >
                <span className="text-xl">📊</span>
                <span className="text-xs font-medium">Charts</span>
              </button>

              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex flex-col items-center gap-1 p-3 rounded-2xl text-white/60 hover:text-white/80 hover:bg-white/10 transition-all duration-300 disabled:opacity-50"
              >
                <span className={`text-xl ${refreshing ? 'animate-spin' : ''}`}>🔄</span>
                <span className="text-xs font-medium">Refresh</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {connected && (
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="hidden md:flex fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full items-center justify-center text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 disabled:opacity-50 z-20"
        >
          <span className={`text-xl ${refreshing ? 'animate-spin' : ''}`}>🔄</span>
        </button>
      )}
    </div>
  );
};

export default EnhancedGoogleSheetsApp;
