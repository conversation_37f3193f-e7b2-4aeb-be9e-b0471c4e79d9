@tailwind base;
@tailwind components;
@tailwind utilities;

/* iOS 18 / macOS Sequoia Design System */
:root {
  /* iOS 18 Color Palette */
  --ios-blue: #007AFF;
  --ios-green: #34C759;
  --ios-indigo: #5856D6;
  --ios-orange: #FF9500;
  --ios-pink: #FF2D92;
  --ios-purple: #AF52DE;
  --ios-red: #FF3B30;
  --ios-teal: #5AC8FA;
  --ios-yellow: #FFCC00;

  /* macOS Sequoia Colors */
  --macos-accent: #0066CC;
  --macos-secondary: #8E8E93;
  --macos-tertiary: #C7C7CC;

  /* Dynamic System Colors */
  --system-background: rgba(0, 0, 0, 0.95);
  --system-secondary-background: rgba(28, 28, 30, 0.95);
  --system-tertiary-background: rgba(44, 44, 46, 0.95);
  --system-grouped-background: rgba(0, 0, 0, 1);

  /* Glass Materials */
  --glass-ultra-thin: rgba(255, 255, 255, 0.05);
  --glass-thin: rgba(255, 255, 255, 0.08);
  --glass-regular: rgba(255, 255, 255, 0.12);
  --glass-thick: rgba(255, 255, 255, 0.18);
  --glass-ultra-thick: rgba(255, 255, 255, 0.25);

  /* Vibrancy Effects */
  --vibrancy-light: rgba(255, 255, 255, 0.1);
  --vibrancy-medium: rgba(255, 255, 255, 0.15);
  --vibrancy-heavy: rgba(255, 255, 255, 0.2);

  /* Shadows */
  --shadow-small: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-medium: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-large: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  --shadow-extra-large: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);

  /* Border Radius */
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --radius-extra-large: 24px;
  --radius-continuous: 20px;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* Typography */
  --font-system: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--font-system);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  background: var(--system-grouped-background);
  color: white;
  line-height: 1.6;
  overflow-x: hidden;
}

/* iOS 18 Advanced Animations */
@keyframes ios-spring-in {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  60% {
    opacity: 1;
    transform: scale(1.02) translateY(-2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes ios-slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ios-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes ios-scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes ios-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes ios-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes ios-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes ios-glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--ios-blue), 0 0 10px var(--ios-blue), 0 0 15px var(--ios-blue);
  }
  50% {
    box-shadow: 0 0 10px var(--ios-blue), 0 0 20px var(--ios-blue), 0 0 30px var(--ios-blue);
  }
}

/* iOS 18 Glass Materials */
.ios-glass-ultra-thin {
  background: var(--glass-ultra-thin);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.ios-glass-thin {
  background: var(--glass-thin);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.ios-glass-regular {
  background: var(--glass-regular);
  backdrop-filter: blur(30px) saturate(180%);
  -webkit-backdrop-filter: blur(30px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ios-glass-thick {
  background: var(--glass-thick);
  backdrop-filter: blur(35px) saturate(180%);
  -webkit-backdrop-filter: blur(35px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.25);
}

.ios-glass-ultra-thick {
  background: var(--glass-ultra-thick);
  backdrop-filter: blur(40px) saturate(180%);
  -webkit-backdrop-filter: blur(40px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* iOS 18 Card System */
.ios-card {
  background: var(--glass-regular);
  backdrop-filter: blur(30px) saturate(180%);
  -webkit-backdrop-filter: blur(30px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-medium);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.ios-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.ios-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-large);
  border-color: rgba(255, 255, 255, 0.3);
}

.ios-card:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

/* iOS 18 Button System */
.ios-button {
  background: var(--glass-thin);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-medium);
  padding: var(--spacing-sm) var(--spacing-md);
  color: white;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  min-height: 44px;
}

.ios-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.ios-button:hover::before {
  left: 100%;
}

.ios-button:hover {
  background: var(--glass-regular);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: var(--shadow-small);
}

.ios-button:active {
  transform: scale(0.95);
  background: var(--glass-ultra-thin);
}

.ios-button-primary {
  background: linear-gradient(135deg, var(--ios-blue), #0056CC);
  border: 1px solid rgba(0, 122, 255, 0.3);
  color: white;
}

.ios-button-primary:hover {
  background: linear-gradient(135deg, #0066FF, #004BB5);
  box-shadow: 0 4px 20px rgba(0, 122, 255, 0.4);
}

/* iOS 18 Safe Area Support */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

/* iOS 18 Scroll Behavior */
.ios-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* iOS 18 Focus States */
.ios-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.3);
}

/* iOS 18 Selection */
::selection {
  background: rgba(0, 122, 255, 0.3);
  color: white;
}

/* iOS 18 Haptic Feedback Simulation */
@keyframes ios-haptic {
  0% { transform: scale(1); }
  50% { transform: scale(0.98); }
  100% { transform: scale(1); }
}

.ios-haptic:active {
  animation: ios-haptic 0.1s ease-out;
}

/* iOS 18 Loading States */
.ios-skeleton {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  background-size: 200px 100%;
  animation: ios-shimmer 1.5s infinite;
}

/* iOS 18 Responsive Breakpoints */
@media (max-width: 640px) {
  .ios-card {
    border-radius: var(--radius-medium);
    margin: 0 var(--spacing-xs);
  }

  .ios-button {
    min-height: 48px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .ios-card {
    border-radius: var(--radius-small);
    padding: var(--spacing-md);
  }
}

/* iOS 18 Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --system-background: rgba(0, 0, 0, 1);
    --system-secondary-background: rgba(28, 28, 30, 1);
    --system-tertiary-background: rgba(44, 44, 46, 1);
  }
}

/* iOS 18 Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* iOS 18 High Contrast */
@media (prefers-contrast: high) {
  .ios-card {
    border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .ios-button {
    border: 2px solid rgba(255, 255, 255, 0.5);
  }
}

/* iOS 18 Print Styles */
@media print {
  .ios-card {
    background: white;
    color: black;
    border: 1px solid #ccc;
    box-shadow: none;
  }

  .ios-button {
    background: white;
    color: black;
    border: 1px solid #ccc;
  }
}

/* Advanced animations and effects */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1) rotate(0deg);
  }
  25% {
    transform: translate(30px, -50px) scale(1.1) rotate(90deg);
  }
  50% {
    transform: translate(-20px, 20px) scale(0.9) rotate(180deg);
  }
  75% {
    transform: translate(40px, 30px) scale(1.05) rotate(270deg);
  }
  100% {
    transform: translate(0px, 0px) scale(1) rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow:
      0 0 5px rgba(168, 85, 247, 0.4),
      0 0 10px rgba(168, 85, 247, 0.2),
      inset 0 0 10px rgba(168, 85, 247, 0.1);
  }
  50% {
    box-shadow:
      0 0 20px rgba(168, 85, 247, 0.8),
      0 0 30px rgba(168, 85, 247, 0.4),
      0 0 40px rgba(168, 85, 247, 0.2),
      inset 0 0 20px rgba(168, 85, 247, 0.2);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  100% {
    transform: translateX(200%) skewX(-15deg);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes fadeInScale {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
  }
  50% {
    transform: translateY(-10px) translateX(-15px) rotate(180deg);
  }
  75% {
    transform: translateY(-30px) translateX(5px) rotate(270deg);
  }
}

/* Animation classes */
.animate-blob {
  animation: blob 12s infinite ease-in-out;
}

.animate-blob-slow {
  animation: blob 20s infinite ease-in-out;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-3000 {
  animation-delay: 3s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.animation-delay-5000 {
  animation-delay: 5s;
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-float-slow {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.5s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

.animate-particle {
  animation: particle-float 8s ease-in-out infinite;
}

/* Advanced Glass Morphism */
.glass-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
}

.glass-card-dark {
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0.1) 100%);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
}

.glass-button {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-button:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.15) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.glass-button:active {
  transform: translateY(0px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    #f093fb 50%,
    #f5576c 75%,
    #4facfe 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg,
    #a8edea 0%,
    #fed6e3 25%,
    #d299c2 50%,
    #fef9d7 75%,
    #667eea 100%);
}

.gradient-accent {
  background: linear-gradient(135deg,
    #ff9a9e 0%,
    #fecfef 25%,
    #fecfef 50%,
    #ff9a9e 75%,
    #a8edea 100%);
}

/* Neumorphism effects */
.neu-card {
  background: linear-gradient(145deg, #e6e6e6, #ffffff);
  box-shadow:
    20px 20px 60px #d1d1d1,
    -20px -20px 60px #ffffff,
    inset 5px 5px 10px rgba(0,0,0,0.1),
    inset -5px -5px 10px rgba(255,255,255,0.8);
  border-radius: 20px;
}

.neu-button {
  background: linear-gradient(145deg, #f0f0f0, #cacaca);
  box-shadow:
    5px 5px 10px #bebebe,
    -5px -5px 10px #ffffff;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.neu-button:hover {
  box-shadow:
    2px 2px 5px #bebebe,
    -2px -2px 5px #ffffff;
}

.neu-button:active {
  box-shadow:
    inset 5px 5px 10px #bebebe,
    inset -5px -5px 10px #ffffff;
}

/* Advanced transitions */
.transition-all-smooth {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.transition-elastic {
  transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Custom scrollbar enhanced */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg,
    rgba(168, 85, 247, 0.8) 0%,
    rgba(236, 72, 153, 0.8) 100%);
  border-radius: 10px;
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg,
    rgba(168, 85, 247, 1) 0%,
    rgba(236, 72, 153, 1) 100%);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* Particle system */
.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  opacity: 0.6;
}

.particle-1 {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #ff6b6b, transparent);
}

.particle-2 {
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, #4ecdc4, transparent);
}

.particle-3 {
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, #45b7d1, transparent);
}

.particle-4 {
  width: 5px;
  height: 5px;
  background: radial-gradient(circle, #f9ca24, transparent);
}

.particle-5 {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #6c5ce7, transparent);
}

/* Radial gradient utility */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* Glow effects */
.glow-purple {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
}

.glow-pink {
  box-shadow: 0 0 20px rgba(236, 72, 153, 0.5);
}

.glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

/* Text glow effects */
.text-glow {
  text-shadow: 0 0 10px currentColor;
}

/* Border animations */
@keyframes border-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.border-spin {
  animation: border-spin 2s linear infinite;
}

/* Loading skeleton */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Enhanced focus states */
.focus-ring {
  transition: all 0.2s ease;
}

.focus-ring:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(168, 85, 247, 0.3),
    0 0 20px rgba(168, 85, 247, 0.2);
}

/* Mobile touch feedback */
@media (hover: none) and (pointer: coarse) {
  .touch-feedback:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .text-responsive-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .text-responsive-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .text-responsive-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

/* Custom utilities */
.backdrop-blur-strong {
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
}

.border-gradient {
  border: 1px solid transparent;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05)) padding-box,
              linear-gradient(135deg, rgba(168,85,247,0.5), rgba(236,72,153,0.5)) border-box;
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}
