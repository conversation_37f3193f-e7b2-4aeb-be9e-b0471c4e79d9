<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apple Style Web Interface - Advanced</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --blur-amount: 20px;
            --neon-glow: 0 0 20px rgba(102, 126, 234, 0.8);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background: #000;
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
            min-height: 100vh;
        }

        /* Scroll Progress Bar */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            height: 3px;
            background: var(--primary-gradient);
            z-index: 10000;
            transition: width 0.1s ease;
        }

        /* Animated Background with Particles */
        .background {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
        }

        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(80px);
            animation: float 20s infinite ease-in-out;
            opacity: 0.5;
        }

        .orb1 {
            width: 600px;
            height: 600px;
            background: radial-gradient(circle at 30% 30%, #667eea, transparent);
            top: -200px;
            left: -200px;
            animation-delay: 0s;
        }

        .orb2 {
            width: 500px;
            height: 500px;
            background: radial-gradient(circle at 30% 30%, #f093fb, transparent);
            bottom: -200px;
            right: -200px;
            animation-delay: 5s;
        }

        .orb3 {
            width: 400px;
            height: 400px;
            background: radial-gradient(circle at 30% 30%, #4facfe, transparent);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: 10s;
        }

        /* Particle System */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            pointer-events: none;
            animation: particle-float 10s infinite linear;
        }

        @keyframes particle-float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(720deg);
                opacity: 0;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translate(0, 0) scale(1) rotate(0deg);
            }
            25% {
                transform: translate(100px, -100px) scale(1.1) rotate(90deg);
            }
            50% {
                transform: translate(-100px, 100px) scale(0.9) rotate(180deg);
            }
            75% {
                transform: translate(50px, 50px) scale(1.05) rotate(270deg);
            }
        }

        /* Navigation Bar */
        .navbar {
            background: var(--glass-bg);
            backdrop-filter: blur(var(--blur-amount)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--blur-amount)) saturate(180%);
            border-bottom: 1px solid var(--glass-border);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .navbar.scrolled {
            background: rgba(0, 0, 0, 0.9);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .navbar-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 600;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 3s ease-in-out infinite;
            position: relative;
        }

        .logo::after {
            content: '✦ Design OS';
            position: absolute;
            left: 0;
            top: 0;
            z-index: -1;
            filter: blur(10px);
            opacity: 0.5;
        }

        @keyframes glow {
            0%, 100% {
                filter: brightness(1) drop-shadow(0 0 5px rgba(102, 126, 234, 0.5));
            }
            50% {
                filter: brightness(1.2) drop-shadow(0 0 20px rgba(102, 126, 234, 0.8));
            }
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            position: relative;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            overflow: hidden;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .nav-links a:hover::before {
            width: 100px;
            height: 100px;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-gradient);
            transition: width 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--text-primary);
            text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        /* Hamburger Menu */
        .menu-toggle {
            display: none;
            flex-direction: column;
            gap: 4px;
            cursor: pointer;
            cursor: pointer;
            padding: 5px;
        }

        .menu-toggle span {
            width: 25px;
            height: 2px;
            background: var(--text-primary);
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .menu-toggle.active span:nth-child(2) {
            opacity: 0;
            transform: scaleX(0);
        }

        .menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(5px, -5px);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            text-align: center;
            max-width: 800px;
            animation: fadeInUp 1s ease-out;
            position: relative;
            z-index: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
                filter: blur(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        .hero h1 {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #fff 0%, #667eea 25%, #f093fb 50%, #4facfe 75%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite, textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        @keyframes textGlow {
            from {
                text-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
            }
            to {
                text-shadow: 0 0 30px rgba(102, 126, 234, 0.8), 0 0 50px rgba(240, 147, 251, 0.5);
            }
        }

        .hero p {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
            animation: fadeIn 1.5s ease-out 0.5s both;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeIn 2s ease-out 1s both;
        }

        .btn {
            padding: 1rem 2.5rem;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .btn:active::before {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            border: none;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transform: translateY(0);
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.6), var(--neon-glow);
        }

        .btn-secondary {
            background: var(--glass-bg);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px) scale(1.05);
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        /* Feature Cards */
        .features {
            padding: 5rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 1rem;
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: var(--primary-gradient);
            animation: lineExpand 2s ease-out infinite;
        }

        @keyframes lineExpand {
            0%, 100% {
                width: 100px;
                opacity: 0.5;
            }
            50% {
                width: 200px;
                opacity: 1;
            }
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: var(--glass-bg);
            backdrop-filter: blur(var(--blur-amount));
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            transform: rotate(0deg);
            transition: transform 0.6s;
            opacity: 0;
        }

        .feature-card:hover::before {
            transform: rotate(180deg);
            opacity: 1;
        }

        .feature-card::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 20px;
            padding: 1px;
            background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.5), transparent);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .feature-card:hover::after {
            opacity: 1;
        }

        .feature-card:hover {
            transform: translateY(-10px) rotateX(5deg) scale(1.02);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 
                0 20px 40px rgba(102, 126, 234, 0.3),
                0 0 20px rgba(102, 126, 234, 0.2) inset;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
            position: relative;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: rotate(360deg) scale(1.1);
            box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(102, 126, 234, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover h3 {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .feature-card p {
            color: var(--text-secondary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .feature-card:hover p {
            color: var(--text-primary);
        }

        /* Interactive Elements */
        .interactive-section {
            padding: 5rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .interactive-demo {
            background: var(--glass-bg);
            backdrop-filter: blur(var(--blur-amount));
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .interactive-demo::before {
            content: '';
            position: absolute;
            top: -100%;
            left: -100%;
            width: 300%;
            height: 300%;
            background: conic-gradient(from 0deg at 50% 50%, transparent, rgba(102, 126, 234, 0.1), transparent);
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            100% {
                transform: rotate(360deg);
            }
        }

        /* Toggle Switches */
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .control-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .control-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .toggle-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .toggle-switch {
            width: 60px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            transition: left 0.3s ease;
        }

        .toggle-switch.active::before {
            left: 0;
        }

        .toggle-switch.active {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }

        .toggle-knob {
            width: 26px;
            height: 26px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 1;
        }

        .toggle-switch.active .toggle-knob {
            transform: translateX(30px) rotate(180deg);
        }

        /* Progress Bars */
        .progress-section {
            margin-top: 3rem;
        }

        .progress-item {
            margin-bottom: 2rem;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .progress-bar {
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-gradient);
            border-radius: 3px;
            width: 0;
            transition: width 2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* Loading Spinner */
        .loader {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top-color: var(--text-primary);
            border-radius: 50%;
            animation: spin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
            margin: 2rem auto;
        }

        @keyframes spin {
            100% {
                transform: rotate(360deg);
            }
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 999;
            overflow: hidden;
        }

        .fab::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transform: scale(0);
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .fab:hover::before {
            transform: scale(1);
        }

        .fab:hover {
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.6), var(--neon-glow);
        }

        .fab:active {
            transform: scale(0.95) rotate(90deg);
        }

        /* Modal */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 2000;
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem;
            max-width: 500px;
            width: 90%;
            transform: scale(0.7) translateY(100px);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .modal.active .modal-content {
            transform: scale(1) translateY(0);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                position: fixed;
                top: 60px;
                left: -100%;
                width: 100%;
                height: calc(100vh - 60px);
                background: rgba(0, 0, 0, 0.95);
                flex-direction: column;
                align-items: center;
                justify-content: start;
                padding-top: 2rem;
                transition: left 0.3s ease;
            }
            
            .nav-links.active {
                left: 0;
            }
            
            .menu-toggle {
                display: flex;
            }
            
            .hero h1 {
                font-size: 3rem;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Scroll Progress -->
    <div class="scroll-progress"></div>

    <!-- Animated Background -->
    <div class="background">
        <div class="gradient-orb orb1"></div>
        <div class="gradient-orb orb2"></div>
        <div class="gradient-orb orb3"></div>
        <!-- Particles will be added via JS -->
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="navbar-content">
            <div class="logo">✦ Design OS</div>
            <ul class="nav-links">
                <li><a href="#home">Trang chủ</a></li>
                <li><a href="#features">Tính năng</a></li>
                <li><a href="#demo">Demo</a></li>
                <li><a href="#about">Giới thiệu</a></li>
                <li><a href="#contact">Liên hệ</a></li>
            </ul>
            <div class="menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>Thiết kế Tương lai</h1>
            <p>Trải nghiệm giao diện người dùng thế hệ mới với hiệu ứng động mượt mà và thiết kế tối giản theo phong cách Apple</p>
            <div class="cta-buttons">
                <a href="#" class="btn btn-primary" onclick="showModal()">Bắt đầu ngay</a>
                <a href="#features" class="btn btn-secondary">Tìm hiểu thêm</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">Tính năng nổi bật</h2>
        <p style="text-align: center; color: var(--text-secondary); max-width: 600px; margin: 0 auto 3rem;">Được thiết kế với những công nghệ tiên tiến nhất cho trải nghiệm người dùng hoàn hảo</p>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">✨</div>
                <h3>Animation mượt mà</h3>
                <p>Hiệu ứng chuyển động 60fps với GPU acceleration, tương tác tự nhiên và phản hồi tức thì</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3>Glass Morphism</h3>
                <p>Thiết kế trong suốt với hiệu ứng blur đa lớp, tạo chiều sâu và sự hiện đại cho giao diện</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🌊</div>
                <h3>Gradient động</h3>
                <p>Màu sắc gradient chuyển động liên tục với nhiều lớp, tạo hiệu ứng sống động và thu hút</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>Hiệu suất cao</h3>
                <p>Tối ưu hóa với lazy loading, code splitting và caching để đạt hiệu suất tốt nhất</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <h3>Tương tác thông minh</h3>
                <p>Gesture controls, magnetic effects và micro-interactions cho trải nghiệm người dùng xuất sắc</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔮</div>
                <h3>3D Effects</h3>
                <p>Hiệu ứng 3D transform và perspective tạo chiều sâu thực tế cho các thành phần</p>
            </div>
        </div>
    </section>

    <!-- Interactive Demo -->
    <section class="interactive-section" id="demo">
        <div class="interactive-demo">
            <h2 class="section-title">Thử nghiệm tương tác</h2>
            <p style="color: var(--text-secondary); margin-bottom: 2rem;">Điều khiển các hiệu ứng và xem sự thay đổi real-time</p>
            
            <div class="controls-grid">
                <div class="control-item">
                    <span>Animation Effects</span>
                    <div class="toggle-container">
                        <div class="toggle-switch" onclick="toggleEffect(this, 'animation')">
                            <div class="toggle-knob"></div>
                        </div>
                    </div>
                </div>
                
                <div class="control-item">
                    <span>Particle System</span>
                    <div class="toggle-container">
                        <div class="toggle-switch" onclick="toggleEffect(this, 'particles')">
                            <div class="toggle-knob"></div>
                        </div>
                    </div>
                </div>
                
                <div class="control-item">
                    <span>Neon Glow</span>
                    <div class="toggle-container">
                        <div class="toggle-switch" onclick="toggleEffect(this, 'glow')">
                            <div class="toggle-knob"></div>
                        </div>
                    </div>
                </div>
                
                <div class="control-item">
                    <span>3D Transform</span>
                    <div class="toggle-container">
                        <div class="toggle-switch" onclick="toggleEffect(this, '3d')">
                            <div class="toggle-knob"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="progress-section">
                <div class="progress-item">
                    <div class="progress-label">
                        <span>Performance</span>
                        <span class="progress-value">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" data-progress="95"></div>
                    </div>
                </div>
                
                <div class="progress-item">
                    <div class="progress-label">
                        <span>User Experience</span>
                        <span class="progress-value">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" data-progress="98"></div>
                    </div>
                </div>
                
                <div class="progress-item">
                    <div class="progress-label">
                        <span>Innovation</span>
                        <span class="progress-value">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" data-progress="92"></div>
                    </div>
                </div>
            </div>
            
            <div class="loader"></div>
        </div>
    </section>

    <!-- Modal -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <h2 style="margin-bottom: 1rem;">Chào mừng bạn!</h2>
            <p style="color: var(--text-secondary); margin-bottom: 2rem;">Cảm ơn bạn đã quan tâm đến Design OS. Chúng tôi sẽ liên hệ với bạn sớm nhất có thể.</p>
            <button class="btn btn-primary" onclick="closeModal()">Đóng</button>
        </div>
    </div>

    <!-- Floating Action Button -->
    <div class="fab" onclick="scrollToTop()">↑</div>

    <script>
        // Scroll Progress Bar
        window.addEventListener('scroll', () => {
            const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
            const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrolled = (winScroll / height) * 100;
            document.querySelector('.scroll-progress').style.width = scrolled + '%';
            
            // Navbar scroll effect
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Hamburger Menu
        const menuToggle = document.querySelector('.menu-toggle');
        const navLinks = document.querySelector('.nav-links');
        
        menuToggle.addEventListener('click', () => {
            menuToggle.classList.toggle('active');
            navLinks.classList.toggle('active');
        });

        // Particle System
        function createParticles() {
            const particlesContainer = document.querySelector('.background');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 10 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Toggle Effects
        function toggleEffect(element, effect) {
            element.classList.toggle('active');
            
            switch(effect) {
                case 'animation':
                    const orbs = document.querySelectorAll('.gradient-orb');
                    orbs.forEach(orb => {
                        orb.style.animationPlayState = element.classList.contains('active') ? 'running' : 'paused';
                    });
                    break;
                case 'particles':
                    const particles = document.querySelectorAll('.particle');
                    particles.forEach(particle => {
                        particle.style.display = element.classList.contains('active') ? 'block' : 'none';
                    });
                    break;
                case 'glow':
                    document.body.classList.toggle('glow-effect');
                    break;
                case '3d':
                    const cards = document.querySelectorAll('.feature-card');
                    cards.forEach(card => {
                        card.style.transform = element.classList.contains('active') 
                            ? 'perspective(1000px) rotateY(5deg)' 
                            : 'none';
                    });
                    break;
            }
        }

        // Progress Bars Animation
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -50px 0px'
        };

        const progressObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const progressBars = entry.target.querySelectorAll('.progress-fill');
                    progressBars.forEach(bar => {
                        const progress = bar.getAttribute('data-progress');
                        setTimeout(() => {
                            bar.style.width = progress + '%';
                            const valueElement = bar.closest('.progress-item').querySelector('.progress-value');
                            animateValue(valueElement, 0, parseInt(progress), 2000);
                        }, 200);
                    });
                }
            });
        }, observerOptions);

        const progressSection = document.querySelector('.progress-section');
        if (progressSection) {
            progressObserver.observe(progressSection);
        }

        // Animate number values
        function animateValue(element, start, end, duration) {
            const range = end - start;
            const startTime = performance.now();
            
            function updateValue(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const value = Math.floor(progress * range + start);
                element.textContent = value + '%';
                
                if (progress < 1) {
                    requestAnimationFrame(updateValue);
                }
            }
            
            requestAnimationFrame(updateValue);
        }

        // Modal Functions
        function showModal() {
            document.getElementById('modal').classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            document.getElementById('modal').classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Click outside to close modal
        document.getElementById('modal').addEventListener('click', (e) => {
            if (e.target.id === 'modal') {
                closeModal();
            }
        });

        // Smooth scroll to top
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    // Close mobile menu if open
                    navLinks.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        });

        // Parallax effect
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const heroContent = document.querySelector('.hero-content');
            const orbs = document.querySelectorAll('.gradient-orb');
            
            if (heroContent) {
                heroContent.style.transform = `translateY(${scrolled * 0.5}px)`;
                heroContent.style.opacity = 1 - scrolled / 700;
            }
            
            orbs.forEach((orb, index) => {
                const speed = 0.5 + (index * 0.2);
                orb.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            
            // Trigger initial animations
            setTimeout(() => {
                document.querySelectorAll('.feature-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.style.animation = 'fadeInUp 0.8s ease-out forwards';
                    }, index * 100);
                });
            }, 500);
        });

        // Ripple effect on click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn') || e.target.closest('.btn')) {
                const button = e.target.classList.contains('btn') ? e.target : e.target.closest('.btn');
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');
                
                button.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            }
        });
    </script>
</body>
</html>